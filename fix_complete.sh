#!/bin/bash

echo "🔧 开始全面修复Android Studio编译问题..."

# 1. 完全清理Gradle缓存
echo "🗑️ 步骤1: 完全清理Gradle缓存..."
rm -rf ~/.gradle/caches
rm -rf ~/.gradle/daemon
rm -rf ~/.gradle/wrapper
rm -rf ~/.gradle/native
rm -rf ~/.gradle/jdks

# 2. 清理项目缓存
echo "🧹 步骤2: 清理项目缓存..."
rm -rf .gradle
rm -rf build
rm -rf app/build
rm -rf .idea/caches
rm -rf .idea/libraries

# 3. 验证JDK路径
echo "☕ 步骤3: 验证JDK路径..."
ANDROID_STUDIO_JDK="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
if [ -d "$ANDROID_STUDIO_JDK" ]; then
    echo "✅ 找到Android Studio JDK: $ANDROID_STUDIO_JDK"
    export JAVA_HOME="$ANDROID_STUDIO_JDK"
    echo "已设置JAVA_HOME=$JAVA_HOME"
else
    echo "❌ 未找到Android Studio JDK，请检查Android Studio安装"
fi

# 4. 检查Android SDK
echo "📱 步骤4: 检查Android SDK..."
SDK_DIR="/Users/<USER>/Library/Android/sdk"
if [ -d "$SDK_DIR/platforms/android-33" ]; then
    echo "✅ Android SDK 33 已安装"
    # 尝试修复core-for-system-modules.jar
    if [ ! -f "$SDK_DIR/platforms/android-33/core-for-system-modules.jar" ]; then
        echo "⚠️ core-for-system-modules.jar 缺失，需要重新安装Android SDK 33"
    fi
else
    echo "❌ Android SDK 33 未安装"
fi

# 5. 创建gradle.properties配置
echo "📝 步骤5: 更新gradle.properties..."
cat > gradle.properties << 'EOF'
# Project-wide Gradle settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
android.useAndroidX=true
kotlin.code.style=official
android.nonTransitiveRClass=true

# JDK配置
org.gradle.java.home=/Applications/Android Studio.app/Contents/jbr/Contents/Home
org.gradle.daemon=true
org.gradle.configureondemand=false
org.gradle.caching=false

# 禁用某些可能导致问题的功能
android.enableJetifier=false
android.disableAutomaticComponentCreation=true
EOF

# 6. 首次构建尝试
echo "🔨 步骤6: 尝试构建项目..."
if [ -f "gradlew" ]; then
    chmod +x gradlew
    ./gradlew clean --no-daemon --no-build-cache
fi

echo ""
echo "✅ 修复脚本执行完成！"
echo ""
echo "📌 重要：请按照以下步骤操作："
echo ""
echo "1. 打开 Android Studio"
echo "2. 选择 File → Invalidate Caches... → 勾选所有选项 → Invalidate and Restart"
echo "3. 重启后，打开 SDK Manager (Tools → SDK Manager)"
echo "4. 在 SDK Platforms 标签页，确保 Android 13.0 (API 33) 已安装"
echo "5. 如果未安装或有更新，请安装/更新"
echo "6. 在 SDK Tools 标签页，确保以下已安装："
echo "   - Android SDK Build-Tools 33.0.x"
echo "   - Android SDK Platform-Tools"
echo "7. 点击 Apply 安装所有更新"
echo "8. 重新同步项目：File → Sync Project with Gradle Files"
echo ""
echo "如果仍有问题，可能需要："
echo "- 完全卸载并重新安装 Android SDK 33"
echo "- 或者降级到 Android SDK 32 (修改 app/build.gradle.kts 中的 compileSdk = 32)" 