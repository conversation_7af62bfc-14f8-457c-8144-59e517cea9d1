#!/usr/bin/env python3
"""
连接诊断工具
帮助诊断和解决连接问题
"""

import subprocess
import socket
import time
import sys

class ConnectionDiagnostic:
    def __init__(self):
        self.device_connected = False
        self.app_running = False
        self.server_running = False
        self.device_ip = None
        
    def check_adb_connection(self):
        """检查ADB连接"""
        try:
            result = subprocess.run(['adb', 'devices'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                devices = [line for line in lines if line.strip() and 'device' in line]
                
                if devices:
                    print("✅ ADB连接正常")
                    print(f"   发现设备: {len(devices)}个")
                    for device in devices:
                        print(f"   - {device}")
                    self.device_connected = True
                    return True
                else:
                    print("❌ 没有发现连接的设备")
                    print("   请确保:")
                    print("   1. 设备已连接并开启USB调试")
                    print("   2. 已授权ADB调试")
                    return False
            else:
                print("❌ ADB命令执行失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ ADB命令超时")
            return False
        except FileNotFoundError:
            print("❌ 找不到ADB命令")
            print("   请确保Android SDK已安装并添加到PATH")
            return False
        except Exception as e:
            print(f"❌ ADB检查失败: {e}")
            return False
    
    def check_app_status(self):
        """检查应用状态"""
        try:
            # 检查应用进程
            result = subprocess.run(['adb', 'shell', 'ps | grep virtualdisplaydemo'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                print("✅ 应用正在运行")
                print(f"   进程信息: {result.stdout.strip()}")
                self.app_running = True
                return True
            else:
                print("❌ 应用没有运行")
                print("   尝试启动应用...")
                return self.start_app()
                
        except Exception as e:
            print(f"❌ 检查应用状态失败: {e}")
            return False
    
    def start_app(self):
        """启动应用"""
        try:
            result = subprocess.run([
                'adb', 'shell', 'am', 'start', 
                '-n', 'com.example.virtualdisplaydemo/.MainActivity'
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print("✅ 应用启动命令已发送")
                time.sleep(3)  # 等待应用启动
                
                # 再次检查应用是否运行
                return self.check_app_status()
            else:
                print("❌ 应用启动失败")
                print(f"   错误: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 启动应用失败: {e}")
            return False
    
    def get_device_ip(self):
        """获取设备IP地址"""
        try:
            # 尝试多种方法获取IP
            commands = [
                "ip addr show wlan0 | grep 'inet ' | awk '{print $2}' | cut -d/ -f1",
                "ifconfig wlan0 | grep 'inet ' | awk '{print $2}'",
                "getprop dhcp.wlan0.ipaddress"
            ]
            
            for cmd in commands:
                try:
                    result = subprocess.run(['adb', 'shell', cmd], 
                                          capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0 and result.stdout.strip():
                        ip = result.stdout.strip()
                        if self.is_valid_ip(ip):
                            self.device_ip = ip
                            print(f"✅ 设备IP地址: {ip}")
                            return ip
                            
                except:
                    continue
            
            print("❌ 无法获取设备IP地址")
            print("   请手动检查设备网络设置")
            return None
            
        except Exception as e:
            print(f"❌ 获取IP地址失败: {e}")
            return None
    
    def is_valid_ip(self, ip):
        """验证IP地址格式"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False
    
    def check_network_server(self):
        """检查网络服务器状态"""
        try:
            # 检查端口是否开放
            result = subprocess.run(['adb', 'shell', 'netstat -an | grep 12345'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                print("✅ 端口12345已开放")
                print(f"   端口信息: {result.stdout.strip()}")
                self.server_running = True
                return True
            else:
                print("❌ 端口12345未开放")
                print("   网络服务器可能没有启动")
                return False
                
        except Exception as e:
            print(f"❌ 检查网络服务器失败: {e}")
            return False
    
    def test_connection(self, ip=None, port=12345):
        """测试连接"""
        if not ip:
            ip = self.device_ip or "localhost"
        
        try:
            print(f"🔗 测试连接到 {ip}:{port}")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((ip, port))
            sock.close()
            
            if result == 0:
                print("✅ 连接测试成功")
                return True
            else:
                print(f"❌ 连接测试失败 (错误码: {result})")
                return False
                
        except Exception as e:
            print(f"❌ 连接测试异常: {e}")
            return False
    
    def check_app_logs(self):
        """检查应用日志"""
        try:
            print("📋 检查应用日志...")
            
            # 获取最近的日志
            result = subprocess.run([
                'adb', 'logcat', '-d', '-s', 
                'NetworkServerManager:*', 'DSMS_MainActivity:*'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')[-10:]  # 最近10行
                print("   最近的应用日志:")
                for line in lines:
                    print(f"   {line}")
            else:
                print("   没有找到相关日志")
                
        except Exception as e:
            print(f"❌ 检查日志失败: {e}")
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始连接诊断...")
        print("=" * 50)
        
        # 1. 检查ADB连接
        print("\n1️⃣ 检查ADB连接")
        if not self.check_adb_connection():
            return False
        
        # 2. 检查应用状态
        print("\n2️⃣ 检查应用状态")
        self.check_app_status()
        
        # 3. 获取设备IP
        print("\n3️⃣ 获取设备IP地址")
        self.get_device_ip()
        
        # 4. 检查网络服务器
        print("\n4️⃣ 检查网络服务器")
        self.check_network_server()
        
        # 5. 测试连接
        print("\n5️⃣ 测试连接")
        if self.device_ip:
            self.test_connection(self.device_ip)
        self.test_connection("localhost")
        
        # 6. 检查日志
        print("\n6️⃣ 检查应用日志")
        self.check_app_logs()
        
        # 7. 总结和建议
        print("\n" + "=" * 50)
        self.print_summary()
        
        return True
    
    def print_summary(self):
        """打印诊断总结"""
        print("📊 诊断总结:")
        print(f"   ADB连接: {'✅' if self.device_connected else '❌'}")
        print(f"   应用运行: {'✅' if self.app_running else '❌'}")
        print(f"   服务器运行: {'✅' if self.server_running else '❌'}")
        print(f"   设备IP: {self.device_ip or '未知'}")
        
        print("\n💡 建议:")
        if not self.device_connected:
            print("   1. 检查设备连接和ADB设置")
        elif not self.app_running:
            print("   1. 手动启动Android应用")
            print("   2. 检查应用是否正确安装")
        elif not self.server_running:
            print("   1. 确保应用已完全启动")
            print("   2. 检查应用权限设置")
            print("   3. 查看应用日志排查问题")
        elif self.device_ip:
            print(f"   1. 使用设备IP连接: {self.device_ip}")
            print("   2. 确保设备和电脑在同一网络")
        else:
            print("   1. 检查网络设置")
            print("   2. 尝试重启应用")

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("🔍 连接诊断工具")
        print("用法: python3 diagnose_connection.py")
        print("功能: 自动诊断Android应用连接问题")
        return
    
    diagnostic = ConnectionDiagnostic()
    diagnostic.run_diagnosis()

if __name__ == "__main__":
    main()
