package com.example.virtualdisplaydemo;

import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.virtualdisplaydemo.callback.ActivityCallback;
import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class MainActivity extends AppCompatActivity implements ActivityCallback {
    private static final String TAG = "DSMS_MainActivity";
    private LinearLayout virtualScreensContainerLayout;
    private Button btnAddVirtualScreenManually;

    private @Nullable VirtualDisplayManagementService mService;
    private boolean mIsServiceBound = false;

    private final Map<Integer, SurfaceViewItem> mActiveSurfaceViewItems = new ConcurrentHashMap<>();
    private MediaProjectionManager mMediaProjectionManager;
    private static @Nullable MediaProjection sMediaProjection; // 静态以在Activity重建时保持（但更好的做法是服务持有或非配置实例）
    private ActivityResultLauncher<Intent> mMediaProjectionPermissionLauncher;
    private boolean mIsMediaProjectionPermissionGranted = false; // 跟踪权限状态
    private final AtomicInteger mManualClientIdCounter = new AtomicInteger(1000); // 用于手动添加屏幕的ID

    private Handler mStatusCheckHandler;
    private Runnable mStatusCheckRunnable;
    private static final long STATUS_CHECK_INTERVAL_MS = 3000; // 状态检查间隔

    // 用于在请求MediaProjection权限时保存挂起的服务请求
    private static class PendingServiceRequestAwaitingMP {
        final String serviceRequestId;
        final String userIdentifier;
        final int assignedInternalClientId;
        PendingServiceRequestAwaitingMP(String serviceRequestId, String userIdentifier, int assignedInternalClientId) {
            this.serviceRequestId = serviceRequestId;
            this.userIdentifier = userIdentifier;
            this.assignedInternalClientId = assignedInternalClientId;
        }
    }
    private @Nullable volatile PendingServiceRequestAwaitingMP mPendingRequestAwaitingMP = null;
    private final Object mPendingMPRequestLock = new Object();


    private static class SurfaceViewItem {
        final View rootView;
        final SurfaceView surfaceView;
        final TextView tvScreenInfoLabel;
        final Button btnRemoveScreen;
        final String initialUserIdentifier;
        final int internalClientId;
        @Nullable String serviceRequestIdForNetworkVD; // 仅网络创建的VD有此ID
        final boolean isManuallyCreated;
        @Nullable String currentOccupyingUserDisplayInfo; // 存储完整的占用信息，包括时间
        int displayId = -1; // Android系统的Display ID
        long lastStatusUpdateTime = 0; // 时间戳，记录核心状态上次改变的时间
        boolean isSurfaceValid = false; // SurfaceView的Surface是否可用

        SurfaceViewItem(View rootView, SurfaceView surfaceView, TextView tvScreenInfoLabel, Button btnRemoveScreen,
                        int internalClientId, String initialUserIdentifier, @Nullable String serviceRequestIdForNetworkVD, boolean isManuallyCreated) {
            this.rootView = rootView;
            this.surfaceView = surfaceView;
            this.tvScreenInfoLabel = tvScreenInfoLabel;
            this.btnRemoveScreen = btnRemoveScreen;
            this.internalClientId = internalClientId;
            this.initialUserIdentifier = initialUserIdentifier;
            this.serviceRequestIdForNetworkVD = serviceRequestIdForNetworkVD;
            this.isManuallyCreated = isManuallyCreated;
            this.lastStatusUpdateTime = System.currentTimeMillis(); // 初始化时间戳
        }

        // 辅助方法：从完整的占用信息字符串中提取“核心”部分（不包含动态变化的时间）
        private @Nullable String getCoreOccupantInfo(@Nullable String fullInfo) {
            if (fullInfo == null) return null;
            // 示例："网络:User (ConnID: XXXX, YYYs)" -> "网络:User (ConnID: XXXX)"
            if (fullInfo.startsWith("网络:")) {
                int timePartStartIndex = fullInfo.lastIndexOf(", ");
                // 确保 ", " 存在且后面是 "s)" 结尾
                if (timePartStartIndex > "网络:".length() && fullInfo.endsWith("s)")) {
                    return fullInfo.substring(0, timePartStartIndex) + ")";
                }
            }
            // 对于非网络类型或格式不匹配的，返回完整信息作为核心信息
            return fullInfo;
        }

        // 更新占用状态和UI文本
        void updateOccupationStatus(@Nullable String newFullOccupyingUserDisplayInfo, boolean isOccupied) {
            String newCoreInfo = getCoreOccupantInfo(newFullOccupyingUserDisplayInfo);
            String currentCoreInfo = getCoreOccupantInfo(this.currentOccupyingUserDisplayInfo);

            boolean coreStatusActuallyChanged = false;
            if ((newCoreInfo == null && currentCoreInfo != null) || (newCoreInfo != null && !newCoreInfo.equals(currentCoreInfo))) {
                coreStatusActuallyChanged = true;
            }

            // 总是用最新的完整信息更新内部状态，以便UI显示最新的时间
            this.currentOccupyingUserDisplayInfo = newFullOccupyingUserDisplayInfo;

            if (coreStatusActuallyChanged) {
                this.lastStatusUpdateTime = System.currentTimeMillis(); // 只有核心状态改变时才重置时间戳
                Log.i(TAG, "SurfaceViewItem ID " + internalClientId + ": CORE STATUS CHANGED. NewFullInfo='" + newFullOccupyingUserDisplayInfo + "'. LastUpdate set to " + this.lastStatusUpdateTime);
            } else {
                Log.d(TAG, "SurfaceViewItem ID " + internalClientId + ": Core status NOT changed (only time part may have updated). NewFullInfo='" + newFullOccupyingUserDisplayInfo + "'. LastUpdate REMAINS " + this.lastStatusUpdateTime);
            }
            updateInfoText(); // 总是调用以刷新UI，特别是时间部分
        }

        // 更新显示的文本信息
        void updateInfoText() {
            if (tvScreenInfoLabel == null) return;

            String statusTextPart = "";
            String statusIndicator;
            boolean isNetworkOccupiedDisplay = false; // 新增标志

            if (currentOccupyingUserDisplayInfo != null) {
                if (currentOccupyingUserDisplayInfo.startsWith("应用:")) {
                    statusTextPart = " - [运行中: " + currentOccupyingUserDisplayInfo.substring("应用:".length()) + "]";
                    statusIndicator = "🟢 ";
                } else if (currentOccupyingUserDisplayInfo.startsWith("网络:")) {
                    statusTextPart = " - [网络占用: " + currentOccupyingUserDisplayInfo.substring("网络:".length()) + "]";
                    statusIndicator = "🟡 ";
                    isNetworkOccupiedDisplay = true; // 标记为网络占用显示
                } else if (currentOccupyingUserDisplayInfo.startsWith("预占用:")) {
                    statusTextPart = " - [" + currentOccupyingUserDisplayInfo + "]";
                    statusIndicator = "⏳ ";
                } else {
                    statusTextPart = " - [" + currentOccupyingUserDisplayInfo + "]";
                    statusIndicator = "🟣 ";
                }
            } else { // 空闲
                statusTextPart = " - [空闲可用]";
                statusIndicator = isManuallyCreated ? "⚪ " : "⚫ ";
            }

            String displayIdPart = (displayId != -1) ? ", Disp: " + displayId : (isSurfaceValid ? ", Disp: 等待中..." : ", Disp: 未就绪");
            String typePart = isManuallyCreated ? "(手动)" : "(网络)";

            String timeInfo = ""; // 默认无独立时间信息
            if (!isNetworkOccupiedDisplay) { // 只有非网络占用状态，才显示独立的 [Ns 前]
                long currentTime = System.currentTimeMillis();
                if (lastStatusUpdateTime == 0 || lastStatusUpdateTime > currentTime) {
                    lastStatusUpdateTime = currentTime - 1000;
                }
                long secondsAgo = Math.max(0, (currentTime - lastStatusUpdateTime) / 1000);
                timeInfo = " [" + secondsAgo + "s 前]";
            }
            // 对于网络占用的屏幕，时间信息已经包含在 statusTextPart 中的 YYYs 部分了
            String content = statusIndicator +
                    "用户: " +
                    initialUserIdentifier +
                    " " +
                    typePart +
                    " (ID: " +
                    internalClientId +
                    displayIdPart +
                    ")" +
                    statusTextPart +
                    timeInfo;
            tvScreenInfoLabel.setText(content);
        }
    }

    private final ServiceConnection mServiceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName className, IBinder serviceBinder) {
            VirtualDisplayManagementService.LocalBinder binder = (VirtualDisplayManagementService.LocalBinder) serviceBinder;
            mService = binder.getService();
            binder.setActivityCallback(MainActivity.this);
            mIsServiceBound = true;
            Log.i(TAG, "DSMS服务已连接，ActivityCallback已注册。");
            Toast.makeText(MainActivity.this, "DSMS服务已连接", Toast.LENGTH_SHORT).show();

            if (mStatusCheckHandler != null && mStatusCheckRunnable != null) {
                mStatusCheckHandler.removeCallbacks(mStatusCheckRunnable);
                mStatusCheckHandler.post(mStatusCheckRunnable); // 服务连接后立即检查一次
            }

            synchronized (mPendingMPRequestLock) {
                if (mPendingRequestAwaitingMP != null && mIsMediaProjectionPermissionGranted && mService != null) {
                    Log.i(TAG, "服务已绑定且MP已就绪，处理挂起的请求: " + mPendingRequestAwaitingMP.serviceRequestId);
                    PendingServiceRequestAwaitingMP pending = mPendingRequestAwaitingMP;
                    mPendingRequestAwaitingMP = null;
                    if ("MANUAL_ADD_INTERNAL_TRIGGER".equals(pending.serviceRequestId)) {
                        int manualClientId = mManualClientIdCounter.getAndIncrement(); String userIdentifierForManual = "ManualUser-" + manualClientId;
                        initiateUiForVirtualScreen(manualClientId, userIdentifierForManual, null, true);
                    } else { mService.retryRequestAfterMediaProjection(pending.serviceRequestId, pending.userIdentifier, pending.assignedInternalClientId); }
                }
            }
        }
        @Override
        public void onServiceDisconnected(ComponentName arg0) {
            Log.w(TAG, "DSMS服务意外断开连接。");
            if (mService != null) { try { IBinder binder = mService.onBind(new Intent(MainActivity.this, VirtualDisplayManagementService.class)); if (binder instanceof VirtualDisplayManagementService.LocalBinder) { ((VirtualDisplayManagementService.LocalBinder) binder).setActivityCallback(null); }
            } catch (Exception e) { Log.w(TAG, "在onServiceDisconnected中清除回调时出错: " + e.getMessage()); }
            }
            mIsServiceBound = false; mService = null;
            Toast.makeText(MainActivity.this, "DSMS服务已断开", Toast.LENGTH_SHORT).show();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        virtualScreensContainerLayout = findViewById(R.id.virtualScreensContainer);
        btnAddVirtualScreenManually = findViewById(R.id.btnAddVirtualScreen);
        mMediaProjectionManager = (MediaProjectionManager) getSystemService(Context.MEDIA_PROJECTION_SERVICE);

        mMediaProjectionPermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    synchronized (mPendingMPRequestLock) {
                        if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                            Log.i(TAG, "用户已授予MediaProjection权限。");
                            if (sMediaProjection != null) { sMediaProjection.stop(); } // 停止旧的（如果有）
                            sMediaProjection = mMediaProjectionManager.getMediaProjection(result.getResultCode(), result.getData());
                            mIsMediaProjectionPermissionGranted = true;
                            Toast.makeText(this, "屏幕捕获权限已授予", Toast.LENGTH_SHORT).show();
                            if (mPendingRequestAwaitingMP != null) {
                                Log.i(TAG, "MediaProjection权限已授予，处理挂起请求: " + mPendingRequestAwaitingMP.serviceRequestId);
                                PendingServiceRequestAwaitingMP pending = mPendingRequestAwaitingMP; mPendingRequestAwaitingMP = null;
                                if (mIsServiceBound && mService != null) {
                                    if ("MANUAL_ADD_INTERNAL_TRIGGER".equals(pending.serviceRequestId)) {
                                        int manualClientId = mManualClientIdCounter.getAndIncrement(); String userIdentifierForManual = "ManualUser-" + manualClientId;
                                        initiateUiForVirtualScreen(manualClientId, userIdentifierForManual, null, true);
                                    } else { mService.retryRequestAfterMediaProjection(pending.serviceRequestId, pending.userIdentifier, pending.assignedInternalClientId); }
                                } else { Log.w(TAG, "MP权限授予后，服务未绑定，请求 " + pending.serviceRequestId + " 可能需要等待服务连接。重新设置为挂起。"); mPendingRequestAwaitingMP = pending; }
                            }
                        } else { // 权限被拒绝
                            Log.e(TAG, "用户拒绝或取消了MediaProjection权限。");
                            Toast.makeText(this, "屏幕捕获权限被拒绝。无法创建虚拟显示器。", Toast.LENGTH_LONG).show();
                            mIsMediaProjectionPermissionGranted = false; if (sMediaProjection != null) { sMediaProjection.stop(); sMediaProjection = null; }
                            if (mPendingRequestAwaitingMP != null) {
                                PendingServiceRequestAwaitingMP pending = mPendingRequestAwaitingMP; mPendingRequestAwaitingMP = null;
                                if (!"MANUAL_ADD_INTERNAL_TRIGGER".equals(pending.serviceRequestId) && mIsServiceBound && mService != null) {
                                    mService.notifyMediaProjectionDenied(pending.serviceRequestId);
                                } else if ("MANUAL_ADD_INTERNAL_TRIGGER".equals(pending.serviceRequestId)) { Toast.makeText(this, "手动添加屏幕的权限请求被拒绝", Toast.LENGTH_SHORT).show(); }
                            }
                        }
                    }
                });
        startAndBindService();
        btnAddVirtualScreenManually.setOnClickListener(v -> {
            Log.d(TAG, "手动'添加虚拟屏幕'按钮被点击。");
            synchronized (mPendingMPRequestLock) {
                if (!mIsMediaProjectionPermissionGranted || sMediaProjection == null) {
                    Log.i(TAG, "手动添加: MediaProjection权限尚未授予或已失效，正在请求权限。");
                    if (mPendingRequestAwaitingMP == null) {
                        mPendingRequestAwaitingMP = new PendingServiceRequestAwaitingMP("MANUAL_ADD_INTERNAL_TRIGGER", "ManualUser_UI_Initiated", -1);
                        Log.d(TAG, "手动添加：设置了挂起请求，等待MP权限。");
                        mMediaProjectionPermissionLauncher.launch(mMediaProjectionManager.createScreenCaptureIntent());
                    } else { Log.w(TAG, "手动添加：已有其他请求 " + mPendingRequestAwaitingMP.serviceRequestId + " 正在等待MP权限。"); Toast.makeText(MainActivity.this, "请等待当前屏幕权限操作完成", Toast.LENGTH_SHORT).show(); }
                } else {
                    Log.i(TAG, "手动添加: MediaProjection已可用，直接创建手动屏幕。");
                    int manualClientId = mManualClientIdCounter.getAndIncrement(); String userIdentifierForManual = "ManualUser-" + manualClientId;
                    initiateUiForVirtualScreen(manualClientId, userIdentifierForManual, null, true);
                }
            }
        });
        initializeStatusMonitoring();
    }

    private void startAndBindService() {
        Intent serviceIntent = new Intent(this, VirtualDisplayManagementService.class);
        try { if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) { startForegroundService(serviceIntent); } else { startService(serviceIntent); }
        } catch (Exception e) { Log.e(TAG, "启动DSMS服务失败: " + e.getMessage(), e); Toast.makeText(this, "启动DSMS服务时出错。", Toast.LENGTH_LONG).show(); }
        if (!bindService(serviceIntent, mServiceConnection, Context.BIND_AUTO_CREATE | Context.BIND_IMPORTANT)) { Log.e(TAG, "绑定到DSMS服务失败。"); Toast.makeText(this, "绑定到DSMS服务时出错。", Toast.LENGTH_LONG).show(); }
    }

    private void initializeStatusMonitoring() {
        if (mStatusCheckHandler == null) { mStatusCheckHandler = new Handler(Looper.getMainLooper()); }
        if (mStatusCheckRunnable == null) {
            mStatusCheckRunnable = new Runnable() {
                @Override
                public void run() {
                    // Log.d(TAG, "mStatusCheckRunnable: Tick! Performing status check.");
                    if (isActivityValidForUiUpdate()) { // 增加检查
                        checkAndUpdateDisplayOccupationStatusFromNetwork();
                    }
                    if (mStatusCheckHandler != null && isActivityValidForUiUpdate()) { // 再次检查
                        mStatusCheckHandler.postDelayed(this, STATUS_CHECK_INTERVAL_MS);
                    } else {
                        Log.d(TAG, "mStatusCheckRunnable: Not rescheduling as handler is null or activity is invalid.");
                    }
                }
            };
        }
        Log.i(TAG, "UI状态监控已初始化，检查间隔: " + STATUS_CHECK_INTERVAL_MS + "ms");
    }



    private boolean isActivityValidForUiUpdate() {
        if (isFinishing() || isDestroyed()) {
            Log.w(TAG, "Activity is finishing or destroyed, skipping UI update.");
            return false;
        }
        return true;
    }

    private void checkAndUpdateDisplayOccupationStatusFromNetwork() {
        if (!mIsServiceBound || mService == null) return;

        // 使用 new ConcurrentHashMap<>(...) 来创建副本进行迭代，避免在迭代时修改原始集合（如果发生）
        for (Map.Entry<Integer, SurfaceViewItem> entry : new ConcurrentHashMap<>(mActiveSurfaceViewItems).entrySet()) {
            SurfaceViewItem item = entry.getValue();
            if (item == null || item.rootView == null || !item.rootView.isAttachedToWindow()) {
                Log.d(TAG, "Item for ID " + entry.getKey() + " is invalid or not attached. Skipping update.");
                continue;
            }

            if (item.displayId != -1) {
                String networkUserInfoFromService = mService.getDisplayNetworkUserInfo(item.internalClientId);
                boolean isActuallyNetworkOccupied = networkUserInfoFromService != null;
                String newUiOccupationInfo;

                if (isActuallyNetworkOccupied) {
                    newUiOccupationInfo = "网络:" + networkUserInfoFromService;
                } else {
                    if (item.currentOccupyingUserDisplayInfo != null && item.currentOccupyingUserDisplayInfo.startsWith("网络:")) {
                        newUiOccupationInfo = null; // 从网络占用变为空闲
                    } else {
                        newUiOccupationInfo = item.currentOccupyingUserDisplayInfo; // 保持当前状态
                    }
                }

                if ((newUiOccupationInfo == null && item.currentOccupyingUserDisplayInfo != null) ||
                        (newUiOccupationInfo != null && !newUiOccupationInfo.equals(item.currentOccupyingUserDisplayInfo))) {
                    Log.d(TAG, "状态检查触发更新 (checkAndUpdate): Item ID " + item.internalClientId + ". OldFull: '" + item.currentOccupyingUserDisplayInfo + "', NewFull: '" + newUiOccupationInfo + "'");
                    item.updateOccupationStatus(newUiOccupationInfo, isActuallyNetworkOccupied);
                }
            }
        }
        // 统一刷新所有可见item的文本信息，以确保时间滚动
        for (SurfaceViewItem item : mActiveSurfaceViewItems.values()) { // 再次迭代原始集合（或副本）
            if (item != null && item.rootView != null && item.rootView.isAttachedToWindow() && item.tvScreenInfoLabel != null) {
                item.updateInfoText();
            }
        }
    }

    @Override
    public void onRequestNewVirtualDisplayUI(String serviceRequestId, String userIdentifier, int assignedInternalClientId) {
        runOnUiThread(() -> {
            if (!isActivityValidForUiUpdate()) return;
            Log.i(TAG, "ActivityCallback: onRequestNewVirtualDisplayUI: ReqID=" + serviceRequestId + ", User=" + userIdentifier + ", ClientID=" + assignedInternalClientId);
            if (!mIsMediaProjectionPermissionGranted || sMediaProjection == null) { Log.e(TAG, "onRequestNewVirtualDisplayUI调用时MP未就绪或已失效。通知Service。"); if(mService != null) mService.notifyMediaProjectionDenied(serviceRequestId); return; }
            initiateUiForVirtualScreen(assignedInternalClientId, userIdentifier, serviceRequestId, false);
        });
    }
    @Override public boolean isMediaProjectionReady() { /* ... (同前) ... */ if (sMediaProjection != null) { try { /* check */ } catch (Exception e) { Log.w(TAG, "sMediaProjection可能已失效: " + e.getMessage()); mIsMediaProjectionPermissionGranted = false; sMediaProjection = null; } } return mIsMediaProjectionPermissionGranted && sMediaProjection != null; }
    @Override public void requestMediaProjectionFromUser(String serviceRequestId) {
        runOnUiThread(() -> { // 确保在UI线程操作Toast和启动Activity
            if (!isActivityValidForUiUpdate()) return;
            Log.i(TAG, "ActivityCallback: requestMediaProjectionFromUser对于请求ID: " + serviceRequestId);
            synchronized (mPendingMPRequestLock) {
                if (mIsMediaProjectionPermissionGranted && sMediaProjection != null) { Log.w(TAG, "requestMediaProjectionFromUser调用，但MP似乎已授予。通知Service重试。"); if (mService != null) mService.retryRequestAfterMediaProjection(serviceRequestId, "User_MP_Reconfirm_From_Activity", -1); return; }
                if (mPendingRequestAwaitingMP == null) { mPendingRequestAwaitingMP = new PendingServiceRequestAwaitingMP(serviceRequestId, "NetworkUser_AwaitingMP_From_Activity", -1); Log.d(TAG, "requestMediaProjectionFromUser: 设置了网络请求挂起状态。启动权限请求。"); mMediaProjectionPermissionLauncher.launch(mMediaProjectionManager.createScreenCaptureIntent());
                } else { Log.w(TAG, "requestMediaProjectionFromUser: 已有请求 " + mPendingRequestAwaitingMP.serviceRequestId + " 正在等待MP权限。新请求 " + serviceRequestId + " 被通知繁忙。"); if (mService != null) mService.notifyMediaProjectionRequestBusy(serviceRequestId, mPendingRequestAwaitingMP.serviceRequestId); }
            }
        });
    }
    @Override
    public void updateManualScreenOccupationStatus(int internalClientId, @Nullable String occupyingUserIdentifierWithPrefix, boolean isOccupied, int displayId) {
        runOnUiThread(() -> {
            if (!isActivityValidForUiUpdate()) return;
            SurfaceViewItem item = mActiveSurfaceViewItems.get(internalClientId);
            if (item != null && item.rootView != null && item.rootView.isAttachedToWindow()) {
                item.displayId = displayId;
                item.updateOccupationStatus(occupyingUserIdentifierWithPrefix, isOccupied);
                Log.i(TAG, "UI更新 (来自Service): 屏幕 " + internalClientId + " (Disp:" + displayId + ") 占用状态已更改。");
            } else {
                Log.w(TAG, "UI更新 (来自Service): 尝试更新internalClientId=" + internalClientId + "的状态，但Item/View无效或未附加。");
            }
        });
    }
    @Override public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientId) { /* Activity 通常无需处理 */ }
    @Override public void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId) { runOnUiThread(() -> { if(isActivityValidForUiUpdate()) Toast.makeText(MainActivity.this, "另一个屏幕权限请求正在处理中 (" + existingRequestId + ")", Toast.LENGTH_SHORT).show();});}


    private void initiateUiForVirtualScreen(final int internalClientId, @NonNull final String initialUserIdentifier,
                                            @Nullable final String serviceRequestIdForNetworkVD, final boolean isManuallyAdded) {
        if (!isActivityValidForUiUpdate()) return;
        if (mActiveSurfaceViewItems.containsKey(internalClientId)) { Log.w(TAG, "InternalClientID " + internalClientId + " 的UI已存在。"); if (serviceRequestIdForNetworkVD != null && mIsServiceBound && mService != null) { mService.notifyVirtualDisplayProcessed(serviceRequestIdForNetworkVD, internalClientId, "ERROR_DUPLICATE_UI_REQUEST_FOR_ID_" + internalClientId); } return; }
        Log.i(TAG, "正在初始化UI: InternalID=" + internalClientId + ", 用户='" + initialUserIdentifier + "', ServiceReqID=" + serviceRequestIdForNetworkVD + ", 是否手动=" + isManuallyAdded);
        LayoutInflater inflater = LayoutInflater.from(this); View screenViewLayout = inflater.inflate(R.layout.item_virtual_screen, virtualScreensContainerLayout, false);
        TextView tvScreenInfo = screenViewLayout.findViewById(R.id.tvClientId); SurfaceView surfaceView = screenViewLayout.findViewById(R.id.surfaceView); Button btnRemove = screenViewLayout.findViewById(R.id.btnRemoveScreen);
        final SurfaceViewItem newItem = new SurfaceViewItem(screenViewLayout, surfaceView, tvScreenInfo, btnRemove, internalClientId, initialUserIdentifier, serviceRequestIdForNetworkVD, isManuallyAdded);
        mActiveSurfaceViewItems.put(internalClientId, newItem);
        newItem.updateInfoText();
        synchronized (mPendingMPRequestLock) {
            if (mPendingRequestAwaitingMP != null) {
                if (isManuallyAdded && "MANUAL_ADD_INTERNAL_TRIGGER".equals(mPendingRequestAwaitingMP.serviceRequestId)) { mPendingRequestAwaitingMP = null; Log.d(TAG,"已清除手动添加的挂起MP请求。");
                } else if (!isManuallyAdded && serviceRequestIdForNetworkVD != null && serviceRequestIdForNetworkVD.equals(mPendingRequestAwaitingMP.serviceRequestId)) { mPendingRequestAwaitingMP = null; Log.d(TAG,"已清除网络请求 " + serviceRequestIdForNetworkVD + " 的挂起MP请求。"); }
            }
        }
        surfaceView.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override public void surfaceCreated(@NonNull SurfaceHolder holder) {
                if (!isActivityValidForUiUpdate()) return;
                newItem.isSurfaceValid = true; Log.i(TAG, "Surface已创建: InternalID=" + newItem.internalClientId); newItem.updateInfoText();
                if (!mIsServiceBound || mService == null) { Log.e(TAG, "Surface创建时服务未绑定 for " + newItem.internalClientId); if (newItem.serviceRequestIdForNetworkVD != null && mService != null) { mService.notifyVirtualDisplayProcessed(newItem.serviceRequestIdForNetworkVD, newItem.internalClientId, "ERROR_SERVICE_UNBOUND_ON_SURFACE_CREATE"); } return; }
                if (!mIsMediaProjectionPermissionGranted || sMediaProjection == null) { Log.e(TAG, "Surface创建时MP未就绪 for " + newItem.internalClientId); if (newItem.serviceRequestIdForNetworkVD != null) { mService.notifyVirtualDisplayProcessed(newItem.serviceRequestIdForNetworkVD, newItem.internalClientId, "ERROR_MP_INVALID_ON_SURFACE_CREATE"); } return; }
                VirtualDisplayContainer vdContainer = mService.createVirtualDisplayForClientWithProjection(newItem.internalClientId, holder.getSurface(), sMediaProjection, newItem.initialUserIdentifier, newItem.isManuallyCreated);
                if (vdContainer != null && vdContainer.getDisplayId() != -1) {
                    newItem.displayId = vdContainer.getDisplayId(); Log.i(TAG, "VD创建请求已发送给Service: InternalID=" + newItem.internalClientId);
                    if (newItem.serviceRequestIdForNetworkVD != null) { mService.notifyVirtualDisplayProcessed(newItem.serviceRequestIdForNetworkVD, newItem.internalClientId, String.valueOf(vdContainer.getDisplayId()));
                    } else if (newItem.isManuallyCreated) { updateManualScreenOccupationStatus(newItem.internalClientId, null, false, vdContainer.getDisplayId()); }
                } else {
                    String errorMsg = (vdContainer != null && vdContainer.getErrorMessage() != null) ? vdContainer.getErrorMessage() : "未知错误"; Log.e(TAG, "VD创建失败 for " + newItem.internalClientId + ": " + errorMsg); newItem.displayId = -1; newItem.updateInfoText(); Toast.makeText(MainActivity.this, "为 " + newItem.initialUserIdentifier + " 创建VD失败: " + errorMsg, Toast.LENGTH_LONG).show();
                    if (newItem.serviceRequestIdForNetworkVD != null) { mService.notifyVirtualDisplayProcessed(newItem.serviceRequestIdForNetworkVD, newItem.internalClientId, "ERROR_VD_CREATION_FAILED_IN_ACTIVITY:" + errorMsg); }
                }
            }
            @Override public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) { /* Log.d(TAG, "Surface已更改: InternalID=" + newItem.internalClientId); */ }
            @Override public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
                runOnUiThread(() -> { // 确保在主线程操作
                    if (!isActivityValidForUiUpdate() && !isFinishing() && !isDestroyed()) { // 即使Activity无效，但如果不是因为正在关闭，也尝试清理
                        Log.w(TAG, "surfaceDestroyed: Activity可能无效，但尝试清理 ID " + newItem.internalClientId);
                    }
                    newItem.isSurfaceValid = false; Log.i(TAG, "Surface已销毁: InternalID=" + newItem.internalClientId);
                    if (mIsServiceBound && mService != null) { mService.releaseVirtualDisplay(newItem.internalClientId, true); }
                    removeSurfaceViewItem(newItem.internalClientId);
                    if (mActiveSurfaceViewItems.isEmpty() && sMediaProjection != null) { Log.i(TAG, "最后一个SurfaceView已销毁，停止全局MediaProjection。"); sMediaProjection.stop(); sMediaProjection = null; mIsMediaProjectionPermissionGranted = false; if(isActivityValidForUiUpdate()) Toast.makeText(MainActivity.this, "MediaProjection已停止。", Toast.LENGTH_SHORT).show();
                        synchronized (mPendingMPRequestLock) { if (mPendingRequestAwaitingMP != null) { Log.w(TAG, "MP已停止，但仍有挂起请求 " + mPendingRequestAwaitingMP.serviceRequestId); if(mService != null && !mPendingRequestAwaitingMP.serviceRequestId.equals("MANUAL_ADD_INTERNAL_TRIGGER")) { mService.notifyMediaProjectionDenied(mPendingRequestAwaitingMP.serviceRequestId); } mPendingRequestAwaitingMP = null; } }
                    }
                });
            }
        });
        btnRemove.setOnClickListener(v -> {
            runOnUiThread(() -> {
                if (!isActivityValidForUiUpdate()) return;
                Log.d(TAG, "'移除屏幕'按钮被点击，针对InternalClient " + newItem.internalClientId);
                if (mIsServiceBound && mService != null) { mService.releaseVirtualDisplay(newItem.internalClientId, false); } // false - 用户操作
                removeSurfaceViewItem(newItem.internalClientId);
            });
        });
        virtualScreensContainerLayout.addView(newItem.rootView); Log.d(TAG, "InternalClient " + newItem.internalClientId + " 的UI已添加到布局。");
    }

    private void removeSurfaceViewItem(int internalClientId) {
        // 确保在主线程上执行UI操作
        if (Looper.myLooper() != Looper.getMainLooper()) {
            runOnUiThread(() -> removeSurfaceViewItem(internalClientId));
            return;
        }

        // 检查Activity状态
        if (isFinishing() || isDestroyed()) {
            Log.w(TAG, "Activity正在销毁，跳过UI移除操作 ID " + internalClientId);
            return;
        }

        SurfaceViewItem removedItem = mActiveSurfaceViewItems.remove(internalClientId);
        if (removedItem != null && removedItem.rootView != null) {
            try {
                if (removedItem.rootView.getParent() instanceof ViewGroup) {
                    Log.d(TAG, "正在从其父容器移除rootView ID " + internalClientId);
                    ((ViewGroup) removedItem.rootView.getParent()).removeView(removedItem.rootView);
                } else {
                    Log.w(TAG, "rootView ID " + internalClientId + " 无父容器或父容器非ViewGroup。");
                }
            } catch (Exception e) {
                Log.e(TAG, "移除rootView时发生异常 ID " + internalClientId + ": " + e.getMessage());
            }
        }
        Log.d(TAG, "SurfaceViewItem for InternalClient " + internalClientId + " 已处理移除。");
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!mIsServiceBound && mService == null) { Log.d(TAG, "onStart: 正在尝试绑定服务。"); startAndBindService();
        } else if (mIsServiceBound && mService != null) { try { IBinder binderObj = mService.onBind(new Intent(this, VirtualDisplayManagementService.class)); if (binderObj instanceof VirtualDisplayManagementService.LocalBinder) { ((VirtualDisplayManagementService.LocalBinder) binderObj).setActivityCallback(this); Log.d(TAG, "onStart: 已向已绑定的服务重新注册ActivityCallback。"); }
        } catch (Exception e) { Log.e(TAG, "onStart: 重新注册回调时出错: " + e.getMessage(), e); }
        }
        if (mStatusCheckHandler != null && mStatusCheckRunnable != null) { mStatusCheckHandler.removeCallbacks(mStatusCheckRunnable); mStatusCheckHandler.post(mStatusCheckRunnable); Log.d(TAG, "onStart: 状态监控已启动/重启。");
        } else { Log.w(TAG, "onStart: 状态监控Handler或Runnable为null。尝试初始化..."); initializeStatusMonitoring(); if (mStatusCheckHandler != null && mStatusCheckRunnable != null) { mStatusCheckHandler.post(mStatusCheckRunnable); } }
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (mStatusCheckHandler != null && mStatusCheckRunnable != null) { mStatusCheckHandler.removeCallbacks(mStatusCheckRunnable); Log.d(TAG, "onStop: 状态监控已停止。"); }
        // 注意：不在onStop中解绑服务或清除ActivityCallback，因为网络请求可能在Activity不可见时到达
        // 只在onDestroy中真正清理
        Log.d(TAG, "onStop: 保持服务绑定以处理网络请求。");
    }

    @Override
    protected void onDestroy() {
        Log.i(TAG, "MainActivity onDestroy - 应用正在退出，通知服务停止所有连接。");

        // 清理性能监控
        if (mPerformanceMonitor != null) {
            mPerformanceMonitor.cleanup();
            mPerformanceMonitor = null;
            Log.i(TAG, "性能监控已清理");
        }

        // 通知服务停止所有网络连接
        if (mIsServiceBound && mService != null) {
            try {
                Log.i(TAG, "onDestroy: 通知服务停止所有网络连接");
                mService.stopAllNetworkConnections();
            } catch (Exception e) {
                Log.w(TAG, "onDestroy中通知服务停止连接时出错: " + e.getMessage());
            }
        }

        if (mStatusCheckHandler != null) { mStatusCheckHandler.removeCallbacksAndMessages(null); mStatusCheckHandler = null; } // 彻底停止Handler
        mStatusCheckRunnable = null;
        if (mIsServiceBound && mService != null) {
            try { if (mService.onBind(new Intent(this, VirtualDisplayManagementService.class)) instanceof VirtualDisplayManagementService.LocalBinder) { ((VirtualDisplayManagementService.LocalBinder)mService.onBind(null)).setActivityCallback(null); }
                unbindService(mServiceConnection); } catch (Exception e) { Log.w(TAG, "onDestroy中解绑服务或清除回调时出错: " + e.getMessage()); }
        }
        mIsServiceBound = false; mService = null;

        // 清理所有UI项
        for (SurfaceViewItem item : mActiveSurfaceViewItems.values()) {
            if (item.rootView != null && item.rootView.getParent() instanceof ViewGroup) {
                ((ViewGroup) item.rootView.getParent()).removeView(item.rootView);
            }
        }
        mActiveSurfaceViewItems.clear();

        if (sMediaProjection != null) { Log.w(TAG, "MainActivity onDestroy: sMediaProjection仍在活动，强制停止。"); sMediaProjection.stop(); sMediaProjection = null; mIsMediaProjectionPermissionGranted = false; }
        super.onDestroy();
    }
}