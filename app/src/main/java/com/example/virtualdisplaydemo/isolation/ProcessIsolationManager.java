package com.example.virtualdisplaydemo.isolation;

import android.content.Context;
import android.util.Log;

import com.example.virtualdisplaydemo.process.LightweightProcessIsolation;
import com.example.virtualdisplaydemo.config.OptimizationConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 进程隔离管理器
 * 
 * 提供改进的进程隔离功能：
 * 1. 会话级别的资源隔离
 * 2. 故障检测和自动恢复
 * 3. 资源使用监控和限制
 * 4. 智能清理和优化
 */
public class ProcessIsolationManager {
    private static final String TAG = "ProcessIsolationManager";
    
    private final Context mContext;
    private final LightweightProcessIsolation mProcessIsolation;
    private final Map<String, IsolatedSessionWrapper> mManagedSessions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService mMonitorExecutor;
    private final AtomicInteger mSessionCounter = new AtomicInteger(1);
    
    // 监控配置
    private static final long HEALTH_CHECK_INTERVAL_MS = 30000; // 30秒健康检查
    private static final long SESSION_TIMEOUT_MS = 600000; // 10分钟会话超时
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    
    public ProcessIsolationManager(Context context) {
        mContext = context.getApplicationContext();
        mProcessIsolation = new LightweightProcessIsolation(context);
        
        mMonitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ProcessIsolationMonitor");
            t.setDaemon(true);
            return t;
        });
        
        startHealthMonitoring();
        Log.i(TAG, "进程隔离管理器已初始化");
    }
    
    /**
     * 创建隔离会话
     */
    public IsolatedSessionWrapper createIsolatedSession(String debuggerId, String packageName, String activityName) {
        // 检查资源限制
        if (!canCreateNewSession()) {
            Log.w(TAG, "无法创建新会话：资源限制");
            return null;
        }
        
        try {
            // 创建底层隔离会话
            LightweightProcessIsolation.IsolatedSession session = 
                mProcessIsolation.createSession(debuggerId, packageName, activityName);
            
            if (session == null) {
                Log.e(TAG, "创建底层隔离会话失败");
                return null;
            }
            
            // 创建包装器
            String sessionId = generateSessionId(debuggerId);
            IsolatedSessionWrapper wrapper = new IsolatedSessionWrapper(sessionId, session, debuggerId, packageName, activityName);
            
            mManagedSessions.put(sessionId, wrapper);
            Log.i(TAG, "创建隔离会话成功: " + sessionId);
            
            return wrapper;
            
        } catch (Exception e) {
            Log.e(TAG, "创建隔离会话时出错", e);
            return null;
        }
    }
    
    /**
     * 销毁隔离会话
     */
    public boolean destroySession(String sessionId) {
        IsolatedSessionWrapper wrapper = mManagedSessions.remove(sessionId);
        if (wrapper == null) {
            Log.w(TAG, "会话不存在: " + sessionId);
            return false;
        }
        
        try {
            wrapper.terminate();
            mProcessIsolation.destroySession(wrapper.getUnderlyingSession().getSessionId());
            
            Log.i(TAG, "销毁隔离会话成功: " + sessionId);
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "销毁隔离会话时出错: " + sessionId, e);
            return false;
        }
    }
    
    /**
     * 获取会话
     */
    public IsolatedSessionWrapper getSession(String sessionId) {
        return mManagedSessions.get(sessionId);
    }
    
    /**
     * 获取所有活跃会话
     */
    public Map<String, IsolatedSessionWrapper> getAllSessions() {
        return new ConcurrentHashMap<>(mManagedSessions);
    }
    
    /**
     * 检查是否可以创建新会话
     */
    private boolean canCreateNewSession() {
        // 检查会话数量限制
        if (mManagedSessions.size() >= OptimizationConfig.getOptimalMaxDisplays()) {
            return false;
        }
        
        // 检查内存使用率
        if (OptimizationConfig.shouldRejectNewConnections()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 启动健康监控
     */
    private void startHealthMonitoring() {
        mMonitorExecutor.scheduleAtFixedRate(this::performHealthCheck,
            HEALTH_CHECK_INTERVAL_MS, HEALTH_CHECK_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        Log.d(TAG, "健康监控已启动");
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            long currentTime = System.currentTimeMillis();
            
            for (IsolatedSessionWrapper wrapper : mManagedSessions.values()) {
                // 检查会话健康状态
                if (!wrapper.isHealthy()) {
                    Log.w(TAG, "检测到不健康的会话: " + wrapper.getSessionId());
                    handleUnhealthySession(wrapper);
                }
                
                // 检查会话超时
                if ((currentTime - wrapper.getLastActivityTime()) > SESSION_TIMEOUT_MS) {
                    Log.i(TAG, "会话超时，自动清理: " + wrapper.getSessionId());
                    destroySession(wrapper.getSessionId());
                }
            }
            
            // 执行内存清理
            if (OptimizationConfig.shouldAggressiveCleanup()) {
                performMemoryCleanup();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "健康检查时出错", e);
        }
    }
    
    /**
     * 处理不健康的会话
     */
    private void handleUnhealthySession(IsolatedSessionWrapper wrapper) {
        try {
            if (wrapper.getRetryCount() < MAX_RETRY_ATTEMPTS) {
                Log.i(TAG, "尝试恢复会话: " + wrapper.getSessionId() + 
                      " (重试次数: " + wrapper.getRetryCount() + ")");
                
                boolean recovered = wrapper.attemptRecovery();
                if (recovered) {
                    Log.i(TAG, "会话恢复成功: " + wrapper.getSessionId());
                } else {
                    Log.w(TAG, "会话恢复失败: " + wrapper.getSessionId());
                }
            } else {
                Log.w(TAG, "会话重试次数已达上限，强制销毁: " + wrapper.getSessionId());
                destroySession(wrapper.getSessionId());
            }
        } catch (Exception e) {
            Log.e(TAG, "处理不健康会话时出错: " + wrapper.getSessionId(), e);
        }
    }
    
    /**
     * 执行内存清理
     */
    private void performMemoryCleanup() {
        try {
            Log.i(TAG, "执行内存清理");
            
            // 找到最老的会话并清理
            IsolatedSessionWrapper oldestSession = null;
            long oldestTime = Long.MAX_VALUE;
            
            for (IsolatedSessionWrapper wrapper : mManagedSessions.values()) {
                if (wrapper.getCreationTime() < oldestTime) {
                    oldestTime = wrapper.getCreationTime();
                    oldestSession = wrapper;
                }
            }
            
            if (oldestSession != null && mManagedSessions.size() > 2) {
                Log.i(TAG, "清理最老的会话: " + oldestSession.getSessionId());
                destroySession(oldestSession.getSessionId());
            }
            
            // 强制垃圾回收
            if (OptimizationConfig.ENABLE_AUTO_GC) {
                System.gc();
            }
            
        } catch (Exception e) {
            Log.e(TAG, "执行内存清理时出错", e);
        }
    }
    
    /**
     * 生成会话ID
     */
    private String generateSessionId(String debuggerId) {
        return "Isolated_" + debuggerId + "_" + System.currentTimeMillis() + 
               "_" + mSessionCounter.getAndIncrement();
    }
    
    /**
     * 获取系统状态
     */
    public IsolationSystemStatus getSystemStatus() {
        int activeSessions = mManagedSessions.size();
        int healthySessions = 0;
        long totalMemoryUsage = 0;
        
        for (IsolatedSessionWrapper wrapper : mManagedSessions.values()) {
            if (wrapper.isHealthy()) {
                healthySessions++;
            }
            totalMemoryUsage += wrapper.getMemoryUsage();
        }
        
        return new IsolationSystemStatus(
            activeSessions,
            healthySessions,
            totalMemoryUsage,
            OptimizationConfig.getCurrentMemoryUsagePercent()
        );
    }
    
    /**
     * 清理管理器
     */
    public void cleanup() {
        Log.i(TAG, "开始清理进程隔离管理器...");
        
        // 停止健康监控
        mMonitorExecutor.shutdown();
        
        // 销毁所有会话
        for (String sessionId : mManagedSessions.keySet()) {
            destroySession(sessionId);
        }
        
        // 清理底层隔离
        mProcessIsolation.cleanup();
        
        Log.i(TAG, "进程隔离管理器清理完成");
    }
    
    /**
     * 系统状态信息
     */
    public static class IsolationSystemStatus {
        public final int activeSessions;
        public final int healthySessions;
        public final long totalMemoryUsage;
        public final int systemMemoryUsagePercent;
        
        public IsolationSystemStatus(int activeSessions, int healthySessions, 
                                   long totalMemoryUsage, int systemMemoryUsagePercent) {
            this.activeSessions = activeSessions;
            this.healthySessions = healthySessions;
            this.totalMemoryUsage = totalMemoryUsage;
            this.systemMemoryUsagePercent = systemMemoryUsagePercent;
        }
        
        @Override
        public String toString() {
            return "IsolationSystemStatus{" +
                   "sessions=" + activeSessions + 
                   ", healthy=" + healthySessions +
                   ", memory=" + (totalMemoryUsage / 1024 / 1024) + "MB" +
                   ", systemMemory=" + systemMemoryUsagePercent + "%" +
                   '}';
        }
    }
}
