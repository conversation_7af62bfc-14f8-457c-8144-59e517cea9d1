package com.example.virtualdisplaydemo.isolation;

import android.util.Log;

import com.example.virtualdisplaydemo.process.LightweightProcessIsolation;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 隔离会话包装器
 * 
 * 为底层的LightweightProcessIsolation.IsolatedSession提供增强功能：
 * 1. 健康状态监控
 * 2. 故障恢复机制
 * 3. 资源使用统计
 * 4. 生命周期管理
 */
public class IsolatedSessionWrapper {
    private static final String TAG = "IsolatedSessionWrapper";
    
    // 会话基本信息
    private final String sessionId;
    private final String debuggerId;
    private final String packageName;
    private final String activityName;
    private final long creationTime;
    
    // 底层会话
    private final LightweightProcessIsolation.IsolatedSession underlyingSession;
    
    // 状态管理
    private volatile SessionState state;
    private final AtomicLong lastActivityTime;
    private final AtomicInteger retryCount;
    private volatile Exception lastError;
    private volatile long lastErrorTime;
    
    // 资源监控
    private final SessionResourceTracker resourceTracker;
    
    public IsolatedSessionWrapper(String sessionId, 
                                LightweightProcessIsolation.IsolatedSession underlyingSession,
                                String debuggerId, String packageName, String activityName) {
        this.sessionId = sessionId;
        this.underlyingSession = underlyingSession;
        this.debuggerId = debuggerId;
        this.packageName = packageName;
        this.activityName = activityName;
        this.creationTime = System.currentTimeMillis();
        
        this.state = SessionState.CREATED;
        this.lastActivityTime = new AtomicLong(creationTime);
        this.retryCount = new AtomicInteger(0);
        this.resourceTracker = new SessionResourceTracker();
        
        Log.i(TAG, "隔离会话包装器已创建: " + sessionId);
    }
    
    /**
     * 启动会话
     */
    public boolean start() {
        try {
            if (state != SessionState.CREATED && state != SessionState.STOPPED) {
                Log.w(TAG, "会话状态不允许启动: " + sessionId + ", 当前状态: " + state);
                return false;
            }
            
            state = SessionState.STARTING;
            updateActivityTime();
            
            // 启动底层会话
            boolean started = underlyingSession.startApplication();
            if (started) {
                state = SessionState.RUNNING;
                resourceTracker.startTracking();
                
                Log.i(TAG, "会话启动成功: " + sessionId);
                return true;
            } else {
                state = SessionState.ERROR;
                Log.e(TAG, "会话启动失败: " + sessionId);
                return false;
            }
            
        } catch (Exception e) {
            state = SessionState.ERROR;
            recordError(e);
            Log.e(TAG, "启动会话时出错: " + sessionId, e);
            return false;
        }
    }
    
    /**
     * 停止会话
     */
    public void stop() {
        try {
            if (state == SessionState.STOPPED || state == SessionState.TERMINATED) {
                return;
            }
            
            state = SessionState.STOPPING;
            updateActivityTime();
            
            // 停止底层会话
            underlyingSession.stopApplication();
            
            // 停止资源跟踪
            resourceTracker.stopTracking();
            
            state = SessionState.STOPPED;
            Log.i(TAG, "会话已停止: " + sessionId);
            
        } catch (Exception e) {
            recordError(e);
            Log.e(TAG, "停止会话时出错: " + sessionId, e);
        }
    }
    
    /**
     * 终止会话
     */
    public void terminate() {
        try {
            if (state == SessionState.TERMINATED) {
                return;
            }
            
            // 先停止会话
            if (state != SessionState.STOPPED) {
                stop();
            }
            
            // 终止底层会话
            underlyingSession.terminate();
            
            state = SessionState.TERMINATED;
            Log.i(TAG, "会话已终止: " + sessionId);
            
        } catch (Exception e) {
            recordError(e);
            Log.e(TAG, "终止会话时出错: " + sessionId, e);
        }
    }
    
    /**
     * 尝试恢复会话
     */
    public boolean attemptRecovery() {
        try {
            int currentRetryCount = retryCount.incrementAndGet();
            Log.i(TAG, "尝试恢复会话: " + sessionId + " (第" + currentRetryCount + "次)");
            
            // 停止当前会话
            stop();
            
            // 等待一段时间
            Thread.sleep(1000);
            
            // 重新启动
            boolean recovered = start();
            if (recovered) {
                Log.i(TAG, "会话恢复成功: " + sessionId);
                return true;
            } else {
                Log.w(TAG, "会话恢复失败: " + sessionId);
                return false;
            }
            
        } catch (Exception e) {
            recordError(e);
            Log.e(TAG, "恢复会话时出错: " + sessionId, e);
            return false;
        }
    }
    
    /**
     * 检查会话是否健康
     */
    public boolean isHealthy() {
        try {
            // 检查基本状态
            if (state == SessionState.ERROR || state == SessionState.TERMINATED) {
                return false;
            }
            
            // 检查底层会话状态
            if (!underlyingSession.isActive()) {
                Log.w(TAG, "底层会话不活跃: " + sessionId);
                return false;
            }
            
            // 检查资源使用情况
            if (resourceTracker.isResourceExhausted()) {
                Log.w(TAG, "会话资源耗尽: " + sessionId);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            recordError(e);
            Log.e(TAG, "检查会话健康状态时出错: " + sessionId, e);
            return false;
        }
    }
    
    /**
     * 更新活动时间
     */
    public void updateActivityTime() {
        lastActivityTime.set(System.currentTimeMillis());
    }
    
    /**
     * 记录错误
     */
    private void recordError(Exception error) {
        lastError = error;
        lastErrorTime = System.currentTimeMillis();
        resourceTracker.recordError();
    }
    
    /**
     * 获取内存使用量
     */
    public long getMemoryUsage() {
        LightweightProcessIsolation.SessionResourceUsage usage = underlyingSession.getResourceUsage();
        return usage != null ? usage.getMemoryUsage() : 0;
    }
    
    /**
     * 获取资源使用情况
     */
    public SessionResourceUsage getResourceUsage() {
        LightweightProcessIsolation.SessionResourceUsage underlyingUsage = underlyingSession.getResourceUsage();
        
        return new SessionResourceUsage(
            sessionId,
            underlyingUsage != null ? underlyingUsage.getMemoryUsage() : 0,
            underlyingUsage != null ? underlyingUsage.getCpuUsage() : 0,
            underlyingUsage != null ? underlyingUsage.getStorageUsage() : 0,
            resourceTracker.getErrorCount(),
            retryCount.get()
        );
    }
    
    // Getters
    public String getSessionId() { return sessionId; }
    public String getDebuggerId() { return debuggerId; }
    public String getPackageName() { return packageName; }
    public String getActivityName() { return activityName; }
    public long getCreationTime() { return creationTime; }
    public long getLastActivityTime() { return lastActivityTime.get(); }
    public int getRetryCount() { return retryCount.get(); }
    public SessionState getState() { return state; }
    public LightweightProcessIsolation.IsolatedSession getUnderlyingSession() { return underlyingSession; }
    public Exception getLastError() { return lastError; }
    public long getLastErrorTime() { return lastErrorTime; }
    public long getUptime() { return System.currentTimeMillis() - creationTime; }
    
    /**
     * 会话状态枚举
     */
    public enum SessionState {
        CREATED,    // 已创建
        STARTING,   // 启动中
        RUNNING,    // 运行中
        STOPPING,   // 停止中
        STOPPED,    // 已停止
        ERROR,      // 错误状态
        TERMINATED  // 已终止
    }
    
    /**
     * 会话资源跟踪器
     */
    private static class SessionResourceTracker {
        private volatile boolean isTracking = false;
        private long startTime = 0;
        private int errorCount = 0;
        private long lastMemoryCheck = 0;
        private long peakMemoryUsage = 0;
        
        void startTracking() {
            isTracking = true;
            startTime = System.currentTimeMillis();
            errorCount = 0;
        }
        
        void stopTracking() {
            isTracking = false;
        }
        
        void recordError() {
            errorCount++;
        }
        
        boolean isResourceExhausted() {
            // 简单的资源耗尽检查
            return errorCount > 10; // 错误次数过多
        }
        
        int getErrorCount() {
            return errorCount;
        }
        
        long getTrackingDuration() {
            return isTracking ? System.currentTimeMillis() - startTime : 0;
        }
    }
    
    /**
     * 会话资源使用情况
     */
    public static class SessionResourceUsage {
        public final String sessionId;
        public final long memoryUsage;
        public final float cpuUsage;
        public final long storageUsage;
        public final int errorCount;
        public final int retryCount;
        
        public SessionResourceUsage(String sessionId, long memoryUsage, float cpuUsage,
                                  long storageUsage, int errorCount, int retryCount) {
            this.sessionId = sessionId;
            this.memoryUsage = memoryUsage;
            this.cpuUsage = cpuUsage;
            this.storageUsage = storageUsage;
            this.errorCount = errorCount;
            this.retryCount = retryCount;
        }
        
        @Override
        public String toString() {
            return "SessionResourceUsage{" +
                   "sessionId='" + sessionId + '\'' +
                   ", memory=" + (memoryUsage / 1024 / 1024) + "MB" +
                   ", cpu=" + cpuUsage + "%" +
                   ", storage=" + (storageUsage / 1024 / 1024) + "MB" +
                   ", errors=" + errorCount +
                   ", retries=" + retryCount +
                   '}';
        }
    }
}
