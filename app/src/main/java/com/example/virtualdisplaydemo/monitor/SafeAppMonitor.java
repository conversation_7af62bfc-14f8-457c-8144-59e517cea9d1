package com.example.virtualdisplaydemo.monitor;

import android.util.Log;

import com.example.virtualdisplaydemo.manager.AppLaunchManager;
import com.example.virtualdisplaydemo.config.MonitoringConfig;

import java.util.function.BooleanSupplier;

/**
 * 安全的应用监控器 - 根本性解决闪退问题
 * 
 * 设计原则：
 * 1. 应用监控是可选功能，不是必需功能
 * 2. 监控失败不应该影响虚拟屏幕功能
 * 3. 所有异常都被安全处理，不会导致崩溃
 * 4. 应用不存在是正常情况，不是错误
 */
public class SafeAppMonitor {
    private static final String TAG = "SafeAppMonitor";
    private static final int CHECK_INTERVAL_MS = 15000; // 15秒检查间隔，减少频率
    private static final int INITIAL_WAIT_TIME_MS = 5000; // 5秒等待应用启动
    private static final int MAX_CONSECUTIVE_FAILURES = 3; // 最大连续失败次数
    
    private final String mConnectionId;
    private final String mTargetAppPackageName;
    private final AppLaunchManager mAppLaunchManager;
    private final BooleanSupplier mIsClientAliveSupplier;
    private final AppExitCallback mAppExitCallback;
    
    private Thread mMonitorThread;
    private volatile boolean mIsRunning = false;
    private volatile boolean mMonitoringEnabled = true;
    
    /**
     * 应用退出回调接口
     */
    public interface AppExitCallback {
        /**
         * 应用退出时调用
         * @param connectionId 连接ID
         * @param packageName 应用包名
         */
        void onAppExited(String connectionId, String packageName);
    }
    
    public SafeAppMonitor(String connectionId, String targetAppPackageName, 
                         AppLaunchManager appLaunchManager, BooleanSupplier isClientAliveSupplier,
                         AppExitCallback appExitCallback) {
        this.mConnectionId = connectionId;
        this.mTargetAppPackageName = targetAppPackageName;
        this.mAppLaunchManager = appLaunchManager;
        this.mIsClientAliveSupplier = isClientAliveSupplier;
        this.mAppExitCallback = appExitCallback;
    }
    
    /**
     * 启动监控 - 完全安全的启动
     */
    public void start() {
        if (mIsRunning) {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 监控已在运行");
            return;
        }
        
        // 检查是否需要监控
        if (!shouldStartMonitoring()) {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 跳过应用监控（这是正常情况）");
            return;
        }
        
        mIsRunning = true;
        mMonitorThread = new Thread(this::monitorLoop, "SafeAppMonitor-" + mConnectionId);
        mMonitorThread.setDaemon(true); // 守护线程，不阻止应用退出
        mMonitorThread.setUncaughtExceptionHandler((thread, exception) -> {
            Log.e(TAG, "ConnID: " + mConnectionId + " - 监控线程发生未捕获异常（已安全处理）: " + exception.getMessage(), exception);
            mIsRunning = false;
        });
        
        try {
            mMonitorThread.start();
            Log.i(TAG, "ConnID: " + mConnectionId + " - 安全应用监控已启动: " + mTargetAppPackageName);
        } catch (Exception e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 启动监控线程失败（已安全处理）: " + e.getMessage());
            mIsRunning = false;
        }
    }
    
    /**
     * 停止监控
     */
    public void stop() {
        mIsRunning = false;
        if (mMonitorThread != null) {
            mMonitorThread.interrupt();
            try {
                mMonitorThread.join(1000);
            } catch (InterruptedException e) {
                Log.d(TAG, "ConnID: " + mConnectionId + " - 等待监控线程结束被中断");
                Thread.currentThread().interrupt();
            }
        }
        Log.i(TAG, "ConnID: " + mConnectionId + " - 安全应用监控已停止");
    }
    
    /**
     * 禁用监控
     */
    public void disableMonitoring() {
        mMonitoringEnabled = false;
        stop();
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控已禁用");
    }
    
    /**
     * 检查是否应该启动监控 - 使用全局配置
     */
    private boolean shouldStartMonitoring() {
        // 检查全局监控配置
        if (!MonitoringConfig.shouldStartAppMonitoring(mTargetAppPackageName)) {
            return false;
        }

        if (!mMonitoringEnabled) {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 本地监控已被禁用");
            return false;
        }

        // 安全检查应用是否安装
        try {
            if (!mAppLaunchManager.isAppInstalled(mTargetAppPackageName)) {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                      " 未安装，跳过监控（这是正常情况）");
                return false;
            }
        } catch (Exception e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 检查应用安装状态失败，跳过监控: " + e.getMessage());
            return false;
        }

        Log.i(TAG, "ConnID: " + mConnectionId + " - 监控配置: " + MonitoringConfig.getConfigSummary());
        return true;
    }
    
    /**
     * 监控循环 - 完全安全的实现
     */
    private void monitorLoop() {
        try {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 开始安全监控: " + mTargetAppPackageName);
            
            // 等待应用启动
            Thread.sleep(INITIAL_WAIT_TIME_MS);
            
            int consecutiveFailures = 0;
            boolean hasEverFoundApp = false;

            // 使用配置中的参数
            long checkInterval = MonitoringConfig.getAppCheckInterval();
            int maxFailures = MonitoringConfig.getMaxConsecutiveFailures();

            while (mIsRunning && mMonitoringEnabled &&
                   safeCheckClientAlive() && !Thread.currentThread().isInterrupted()) {

                boolean appRunning = safeCheckAppRunning();

                if (appRunning) {
                    hasEverFoundApp = true;
                    consecutiveFailures = 0;
                    Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName + " 正在运行");
                } else {
                    consecutiveFailures++;

                    // 如果从未找到过应用，可能应用不存在或启动失败
                    if (!hasEverFoundApp && consecutiveFailures >= maxFailures) {
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                              " 从未检测到，停止监控（这可能是正常情况）");
                        break;
                    }

                    // 如果曾经找到过应用，现在找不到了，可能是应用退出
                    if (hasEverFoundApp && consecutiveFailures >= maxFailures) {
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName + " 已退出");

                        // 根据配置决定是否断开连接
                        if (MonitoringConfig.shouldDisconnectOnAppExit()) {
                            safeNotifyAppExited();
                        } else {
                            Log.i(TAG, "ConnID: " + mConnectionId + " - 应用已退出，但配置为不断开连接");
                        }
                        break;
                    }

                    Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                          " 检测不到，连续次数: " + consecutiveFailures + "/" + maxFailures +
                          " (曾经找到过: " + hasEverFoundApp + ")");
                }

                // 等待下次检查
                Thread.sleep(checkInterval);
            }
            
        } catch (InterruptedException e) {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 监控线程被中断（正常）");
            Thread.currentThread().interrupt();
        } catch (Throwable t) {
            Log.e(TAG, "ConnID: " + mConnectionId + " - 监控发生严重错误（已安全处理）: " + t.getMessage(), t);
        } finally {
            mIsRunning = false;
            Log.i(TAG, "ConnID: " + mConnectionId + " - 监控循环已结束");
        }
    }
    
    /**
     * 安全检查客户端是否存活
     */
    private boolean safeCheckClientAlive() {
        try {
            return mIsClientAliveSupplier != null && mIsClientAliveSupplier.getAsBoolean();
        } catch (Exception e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 检查客户端状态失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 安全检查应用运行状态
     */
    private boolean safeCheckAppRunning() {
        try {
            return mAppLaunchManager.isAppRunning(mTargetAppPackageName);
        } catch (Throwable t) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 检查应用状态失败（已安全处理）: " + t.getMessage());
            return false;
        }
    }
    
    /**
     * 安全通知应用退出
     */
    private void safeNotifyAppExited() {
        try {
            if (mAppExitCallback != null) {
                mAppExitCallback.onAppExited(mConnectionId, mTargetAppPackageName);
            }
        } catch (Throwable t) {
            Log.e(TAG, "ConnID: " + mConnectionId + " - 通知应用退出失败（已安全处理）: " + t.getMessage(), t);
        }
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return mIsRunning;
    }
    
    /**
     * 检查监控是否启用
     */
    public boolean isMonitoringEnabled() {
        return mMonitoringEnabled;
    }
}
