package com.example.virtualdisplaydemo.monitor;

import android.util.Log;

import com.example.virtualdisplaydemo.manager.AppLaunchManager;

import java.util.function.BooleanSupplier;

/**
 * 应用监控器 - 监控目标应用的运行状态
 */
public class AppMonitor {
    private static final String TAG = "AppMonitor";
    private static final int INITIAL_WAIT_TIME_MS = 10000; // 10秒等待应用启动
    private static final int CHECK_INTERVAL_MS = 10000; // 10秒检查间隔
    private static final int MAX_NOT_FOUND_COUNT = 3; // 连续3次检测不到才认为应用退出
    
    private final String mConnectionId;
    private final String mTargetAppPackageName;
    private final AppLaunchManager mAppLaunchManager;
    private final BooleanSupplier mIsClientAliveSupplier;
    private final Runnable mDisconnectCallback;
    
    private Thread mAppMonitorThread;
    private volatile boolean mIsRunning = false;
    
    public AppMonitor(String connectionId, String targetAppPackageName, 
                     AppLaunchManager appLaunchManager, BooleanSupplier isClientAliveSupplier,
                     Runnable disconnectCallback) {
        mConnectionId = connectionId;
        mTargetAppPackageName = targetAppPackageName;
        mAppLaunchManager = appLaunchManager;
        mIsClientAliveSupplier = isClientAliveSupplier;
        mDisconnectCallback = disconnectCallback;
    }
    
    /**
     * 启动应用监控
     */
    public void start() {
        if (mIsRunning) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 应用监控已在运行");
            return;
        }
        
        if (mTargetAppPackageName == null) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 目标应用包名为空，无法启动监控");
            return;
        }
        
        mIsRunning = true;
        mAppMonitorThread = new Thread(this::monitorLoop, "AppMonitor-" + mConnectionId);
        mAppMonitorThread.start();
        
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控线程已启动，监控应用: " + mTargetAppPackageName);
    }
    
    /**
     * 停止应用监控
     */
    public void stop() {
        mIsRunning = false;
        if (mAppMonitorThread != null) {
            mAppMonitorThread.interrupt();
            try {
                mAppMonitorThread.join(1000);
            } catch (InterruptedException e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 等待应用监控线程结束被中断");
            }
        }
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控线程已结束");
    }
    
    /**
     * 监控循环
     */
    private void monitorLoop() {
        try {
            // 等待应用完全启动
            Log.d(TAG, "ConnID: " + mConnectionId + " - 等待" + (INITIAL_WAIT_TIME_MS/1000) + "秒让应用完全启动...");
            Thread.sleep(INITIAL_WAIT_TIME_MS);
            
            int consecutiveNotFoundCount = 0;
            
            while (mIsRunning && mIsClientAliveSupplier.getAsBoolean() && 
                   !Thread.currentThread().isInterrupted()) {
                
                // 检查目标应用是否还在运行
                if (!mAppLaunchManager.isAppRunning(mTargetAppPackageName)) {
                    consecutiveNotFoundCount++;
                    Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName + 
                          " 检测不到，连续次数: " + consecutiveNotFoundCount);
                    
                    if (consecutiveNotFoundCount >= MAX_NOT_FOUND_COUNT) {
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 连续" + MAX_NOT_FOUND_COUNT + 
                              "次检测到目标应用 " + mTargetAppPackageName + " 已退出，断开客户端连接");
                        
                        // 通知应用退出并断开连接
                        notifyAppExitedAndDisconnect();
                        break;
                    }
                } else {
                    // 应用仍在运行，重置计数器
                    if (consecutiveNotFoundCount > 0) {
                        Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName + 
                              " 重新检测到，重置计数器");
                        consecutiveNotFoundCount = 0;
                    }
                }
                
                // 等待下次检查
                Thread.sleep(CHECK_INTERVAL_MS);
            }
            
        } catch (InterruptedException e) {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 应用监控线程被中断");
        } catch (Exception e) {
            Log.e(TAG, "ConnID: " + mConnectionId + " - 应用监控线程异常: " + e.getMessage());
        }
        
        mIsRunning = false;
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控循环已结束");
    }
    
    /**
     * 通知应用退出并断开连接
     */
    private void notifyAppExitedAndDisconnect() {
        // 这里可以发送应用退出通知给客户端
        // 由于需要PrintWriter，这部分逻辑可能需要在ClientSocketHandler中处理
        
        // 断开连接
        if (mDisconnectCallback != null) {
            mDisconnectCallback.run();
        }
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return mIsRunning;
    }
}
