package com.example.virtualdisplaydemo.monitor;

import android.util.Log;

import com.example.virtualdisplaydemo.manager.AppLaunchManager;

import java.util.function.BooleanSupplier;

/**
 * 应用监控器 - 监控目标应用的运行状态
 */
public class AppMonitor {
    private static final String TAG = "AppMonitor";
    private static final int INITIAL_WAIT_TIME_MS = 10000; // 10秒等待应用启动
    private static final int CHECK_INTERVAL_MS = 10000; // 10秒检查间隔
    private static final int MAX_NOT_FOUND_COUNT = 5; // 连续5次检测不到才认为应用退出
    private static final int MAX_TOTAL_RETRIES = 15; // 总重试次数限制，防止无限重试
    
    private final String mConnectionId;
    private final String mTargetAppPackageName;
    private final AppLaunchManager mAppLaunchManager;
    private final BooleanSupplier mIsClientAliveSupplier;
    private final Runnable mDisconnectCallback;
    
    private Thread mAppMonitorThread;
    private volatile boolean mIsRunning = false;
    
    public AppMonitor(String connectionId, String targetAppPackageName, 
                     AppLaunchManager appLaunchManager, BooleanSupplier isClientAliveSupplier,
                     Runnable disconnectCallback) {
        mConnectionId = connectionId;
        mTargetAppPackageName = targetAppPackageName;
        mAppLaunchManager = appLaunchManager;
        mIsClientAliveSupplier = isClientAliveSupplier;
        mDisconnectCallback = disconnectCallback;
    }
    
    /**
     * 启动应用监控 - 改为可选监控
     */
    public void start() {
        if (mIsRunning) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 应用监控已在运行");
            return;
        }

        if (mTargetAppPackageName == null || mTargetAppPackageName.trim().isEmpty()) {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 目标应用包名为空，跳过应用监控");
            return;
        }

        // 预检查：如果应用不存在，直接跳过监控
        try {
            if (!mAppLaunchManager.isAppInstalled(mTargetAppPackageName)) {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                      " 未安装，跳过监控（这是正常情况）");
                return;
            }
        } catch (Exception e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 预检查应用安装状态失败，跳过监控: " + e.getMessage());
            return;
        }

        mIsRunning = true;
        mAppMonitorThread = new Thread(this::safeMonitorLoop, "AppMonitor-" + mConnectionId);
        mAppMonitorThread.setDaemon(true); // 设置为守护线程，避免阻止应用退出
        mAppMonitorThread.start();

        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控线程已启动，监控应用: " + mTargetAppPackageName);
    }
    
    /**
     * 停止应用监控
     */
    public void stop() {
        mIsRunning = false;
        if (mAppMonitorThread != null) {
            mAppMonitorThread.interrupt();
            try {
                mAppMonitorThread.join(1000);
            } catch (InterruptedException e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 等待应用监控线程结束被中断");
            }
        }
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控线程已结束");
    }
    
    /**
     * 安全的监控循环 - 完全隔离错误，不影响主功能
     */
    private void safeMonitorLoop() {
        try {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 开始安全监控应用: " + mTargetAppPackageName);

            // 等待应用完全启动
            Log.d(TAG, "ConnID: " + mConnectionId + " - 等待" + (INITIAL_WAIT_TIME_MS/1000) + "秒让应用完全启动...");
            Thread.sleep(INITIAL_WAIT_TIME_MS);

            int consecutiveNotFoundCount = 0;
            boolean hasEverFoundApp = false;

            while (mIsRunning && mIsClientAliveSupplier.getAsBoolean() &&
                   !Thread.currentThread().isInterrupted()) {

                // 安全检查应用状态
                boolean appRunning = safeCheckAppRunning();

                if (appRunning) {
                    hasEverFoundApp = true;
                    // 应用在运行，重置计数器
                    if (consecutiveNotFoundCount > 0) {
                        Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                              " 重新检测到，重置计数器");
                        consecutiveNotFoundCount = 0;
                    }
                } else {
                    consecutiveNotFoundCount++;

                    // 如果从未找到过应用，说明应用可能不存在，直接退出监控
                    if (!hasEverFoundApp && consecutiveNotFoundCount >= 3) {
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                              " 从未检测到，可能不存在，停止监控（这是正常情况）");
                        break;
                    }

                    // 如果曾经找到过应用，现在找不到了，可能是应用退出
                    if (hasEverFoundApp && consecutiveNotFoundCount >= MAX_NOT_FOUND_COUNT) {
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                              " 已退出，通知客户端");
                        safeNotifyAppExited();
                        break;
                    }

                    Log.d(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName +
                          " 检测不到，连续次数: " + consecutiveNotFoundCount +
                          " (曾经找到过: " + hasEverFoundApp + ")");
                }

                // 等待下次检查
                Thread.sleep(CHECK_INTERVAL_MS);
            }

        } catch (InterruptedException e) {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 应用监控线程被中断（正常）");
            Thread.currentThread().interrupt(); // 恢复中断状态
        } catch (Throwable t) {
            // 捕获所有异常，包括Error，确保不会导致应用崩溃
            Log.e(TAG, "ConnID: " + mConnectionId + " - 应用监控发生严重错误，但已被安全处理: " + t.getMessage(), t);
        }
        
        mIsRunning = false;
        Log.i(TAG, "ConnID: " + mConnectionId + " - 应用监控循环已结束");
    }
    
    /**
     * 安全检查应用运行状态
     */
    private boolean safeCheckAppRunning() {
        try {
            return mAppLaunchManager.isAppRunning(mTargetAppPackageName);
        } catch (Throwable t) {
            // 捕获所有异常，包括Error
            Log.w(TAG, "ConnID: " + mConnectionId + " - 检查应用状态时出错（已安全处理）: " + t.getMessage());
            // 出错时假设应用不在运行，这样更安全
            return false;
        }
    }

    /**
     * 安全通知应用退出
     */
    private void safeNotifyAppExited() {
        try {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 应用 " + mTargetAppPackageName + " 已退出，准备通知客户端");

            // 这里可以发送应用退出通知给客户端
            // 由于需要PrintWriter，这部分逻辑可能需要在ClientSocketHandler中处理

            // 断开连接（可选）
            if (mDisconnectCallback != null) {
                mDisconnectCallback.run();
            } else {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 无断开回调，仅记录应用退出事件");
            }
        } catch (Throwable t) {
            // 捕获所有异常，确保不会影响其他功能
            Log.e(TAG, "ConnID: " + mConnectionId + " - 通知应用退出时出错（已安全处理）: " + t.getMessage(), t);
        }
    }

    /**
     * 通知应用退出并断开连接 - 保留兼容性
     * @deprecated 使用 safeNotifyAppExited() 替代
     */
    @Deprecated
    private void notifyAppExitedAndDisconnect() {
        safeNotifyAppExited();
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return mIsRunning;
    }
}
