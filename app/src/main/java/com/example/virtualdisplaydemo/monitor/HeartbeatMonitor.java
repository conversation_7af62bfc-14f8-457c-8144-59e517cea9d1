package com.example.virtualdisplaydemo.monitor;

import android.util.Log;

import java.io.PrintWriter;
import java.util.function.BooleanSupplier;

/**
 * 心跳监控器 - 负责维护客户端连接的心跳机制
 */
public class HeartbeatMonitor {
    private static final String TAG = "HeartbeatMonitor";
    private static final int HEARTBEAT_INTERVAL_MS = 10000; // 10秒
    
    private final String mConnectionId;
    private final PrintWriter mWriter;
    private final BooleanSupplier mIsClientAliveSupplier;
    private Thread mHeartbeatThread;
    private volatile boolean mIsRunning = false;
    
    public HeartbeatMonitor(String connectionId, PrintWriter writer, BooleanSupplier isClientAliveSupplier) {
        mConnectionId = connectionId;
        mWriter = writer;
        mIsClientAliveSupplier = isClientAliveSupplier;
    }
    
    /**
     * 启动心跳监控
     */
    public void start() {
        if (mIsRunning) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 心跳监控已在运行");
            return;
        }
        
        mIsRunning = true;
        mHeartbeatThread = new Thread(this::heartbeatLoop, "HeartbeatThread-" + mConnectionId);
        mHeartbeatThread.setDaemon(true);
        mHeartbeatThread.start();
        
        Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已启动。");
    }
    
    /**
     * 停止心跳监控
     */
    public void stop() {
        mIsRunning = false;
        if (mHeartbeatThread != null) {
            mHeartbeatThread.interrupt();
            try {
                mHeartbeatThread.join(1000);
            } catch (InterruptedException e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 等待心跳线程结束被中断");
            }
        }
        Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已停止。");
    }
    
    /**
     * 心跳循环
     */
    private void heartbeatLoop() {
        try {
            while (mIsRunning && mIsClientAliveSupplier.getAsBoolean() && 
                   !Thread.currentThread().isInterrupted()) {
                
                Thread.sleep(HEARTBEAT_INTERVAL_MS);
                
                if (mWriter != null && !mWriter.checkError()) {
                    mWriter.println("DSMS_HEARTBEAT_PING");
                    Log.d(TAG, "ConnID: " + mConnectionId + " - 发送心跳PING");
                } else {
                    Log.w(TAG, "ConnID: " + mConnectionId + " - Writer无效，停止心跳");
                    break;
                }
            }
        } catch (InterruptedException e) {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 心跳线程被中断");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            Log.e(TAG, "ConnID: " + mConnectionId + " - 心跳线程异常: " + e.getMessage());
        }
        
        mIsRunning = false;
        Log.i(TAG, "ConnID: " + mConnectionId + " - 心跳循环已结束");
    }
    
    /**
     * 检查是否正在运行
     */
    public boolean isRunning() {
        return mIsRunning;
    }
}
