package com.example.virtualdisplaydemo.manager;

import android.util.Log;

import androidx.annotation.Nullable;

import com.example.virtualdisplaydemo.model.NetworkClientConnection;
import com.example.virtualdisplaydemo.model.PendingVDRequest;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 客户端连接管理器 - 专门负责管理客户端连接和请求
 */
public class ClientConnectionManager {
    private static final String TAG = "ClientConnectionManager";
    
    private final Map<Integer, NetworkClientConnection> mActiveNetworkConnections = new ConcurrentHashMap<>();
    private final Map<String, PendingVDRequest> mPendingVDRequests = new ConcurrentHashMap<>();
    private final AtomicInteger mInternalClientIdCounter = new AtomicInteger(1);
    
    /**
     * 生成新的内部客户端ID
     */
    public int generateNewInternalClientId() {
        return mInternalClientIdCounter.getAndIncrement();
    }
    
    /**
     * 添加网络连接记录
     */
    public void addNetworkConnection(int internalClientId, String userIdentifier, String connectionId) {
        NetworkClientConnection connection = new NetworkClientConnection(userIdentifier, connectionId);
        mActiveNetworkConnections.put(internalClientId, connection);
        Log.d(TAG, "已为InternalID " + internalClientId + " 添加网络连接记录: User=" + 
              userIdentifier + ", ConnID=" + connectionId);
    }
    
    /**
     * 移除网络连接记录
     */
    public void removeNetworkConnection(int internalClientId) {
        NetworkClientConnection removed = mActiveNetworkConnections.remove(internalClientId);
        if (removed != null) {
            Log.d(TAG, "已移除InternalID " + internalClientId + " 的网络连接记录");
        }
    }
    
    /**
     * 检查显示是否被网络客户端使用
     */
    public boolean isDisplayBeingUsedByNetworkClient(int internalClientId) {
        return mActiveNetworkConnections.containsKey(internalClientId);
    }
    
    /**
     * 获取显示的网络用户信息
     */
    public @Nullable String getDisplayNetworkUserInfo(int internalClientId) {
        NetworkClientConnection connection = mActiveNetworkConnections.get(internalClientId);
        if (connection != null) {
            long connectedDurationMs = connection.getConnectionDuration();
            String connIdPart = connection.getShortConnectionId();
            
            long connectedDurationSec = connectedDurationMs / 1000;
            return "网络:" + connection.userIdentifier + " (连接" + connectedDurationSec + "秒, ID:" + connIdPart + ")";
        }
        return null;
    }
    
    /**
     * 清理所有网络连接记录
     */
    public void clearAllNetworkConnections() {
        Log.i(TAG, "清理 " + mActiveNetworkConnections.size() + " 个网络连接记录");
        mActiveNetworkConnections.clear();
    }
    
    /**
     * 添加待处理的VD请求
     */
    public void addPendingVDRequest(String serviceRequestId, PendingVDRequest request) {
        mPendingVDRequests.put(serviceRequestId, request);
        Log.d(TAG, "添加待处理VD请求: " + serviceRequestId);
    }
    
    /**
     * 移除待处理的VD请求
     */
    public @Nullable PendingVDRequest removePendingVDRequest(String serviceRequestId) {
        PendingVDRequest removed = mPendingVDRequests.remove(serviceRequestId);
        if (removed != null) {
            Log.d(TAG, "移除待处理VD请求: " + serviceRequestId);
        }
        return removed;
    }
    
    /**
     * 获取待处理的VD请求
     */
    public @Nullable PendingVDRequest getPendingVDRequest(String serviceRequestId) {
        return mPendingVDRequests.get(serviceRequestId);
    }
    
    /**
     * 通知虚拟显示处理完成
     */
    public void notifyVirtualDisplayProcessed(String serviceRequestId, int internalClientId, @Nullable String displayIdOrError) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.i(TAG, "接收到VD处理完成通知: ReqID=" + serviceRequestId + 
                  ", InternalID=" + internalClientId + ", 结果=" + displayIdOrError);
            request.setResult(internalClientId, displayIdOrError);
        } else {
            Log.w(TAG, "接收到VD处理通知，但未找到对应的挂起请求ID: " + serviceRequestId);
        }
    }
    
    /**
     * 通知MediaProjection被拒绝
     */
    public void notifyMediaProjectionDenied(String serviceRequestId) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.w(TAG, "接收到MediaProjection被拒绝通知: ReqID=" + serviceRequestId);
            request.setMediaProjectionDenied();
        } else {
            Log.w(TAG, "接收到MP被拒绝通知，但未找到对应的挂起请求ID: " + serviceRequestId);
        }
    }

    /**
     * 通知MediaProjection请求繁忙
     */
    public void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId) {
        PendingVDRequest request = mPendingVDRequests.get(newRequestId);
        if (request != null) {
            Log.w(TAG, "MP请求 " + newRequestId + " 失败，因为已有请求 " + existingRequestId + " 正在等待MP权限。");
            if (request.writer != null) {
                request.writer.println("ERROR:" + newRequestId + ":另一个屏幕权限请求正在处理中，请稍后。");
            }
            request.setResult(request.getAssignedInternalClientId(), "ERROR_MP_REQUEST_BUSY");
        }
    }

    /**
     * 在MediaProjection权限获取后重试请求
     */
    public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientIdFromActivity) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.i(TAG, "MP权限已获取，重新处理请求: " + serviceRequestId);

            // 如果Activity提供了新的内部客户端ID，则使用它
            if (assignedInternalClientIdFromActivity != -1) {
                request.setAssignedInternalClientId(assignedInternalClientIdFromActivity);
            } else if (request.getAssignedInternalClientId() == -1) {
                // 如果还没有分配ID，则生成一个新的
                request.setAssignedInternalClientId(generateNewInternalClientId());
            }

            // 这里需要触发Activity创建UI，但我们需要通过NetworkServerManager来获取ActivityCallback
            // 暂时先记录日志，具体实现需要在NetworkServerManager中完成
            Log.d(TAG, "需要通过NetworkServerManager触发UI创建，请求ID: " + serviceRequestId);
        } else {
            Log.w(TAG, "MP就绪后尝试重试请求，但未找到挂起请求: " + serviceRequestId);
        }
    }
    
    /**
     * 获取活跃连接数量
     */
    public int getActiveConnectionCount() {
        return mActiveNetworkConnections.size();
    }
    
    /**
     * 获取待处理请求数量
     */
    public int getPendingRequestCount() {
        return mPendingVDRequests.size();
    }
}
