package com.example.virtualdisplaydemo.manager;

import android.util.Log;

import androidx.annotation.Nullable;

import com.example.virtualdisplaydemo.model.NetworkClientConnection;
import com.example.virtualdisplaydemo.model.PendingVDRequest;
import com.example.virtualdisplaydemo.config.OptimizationConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 优化的客户端管理器 - 支持连接池化、超时管理和资源优化
 */
public class OptimizedClientManager {
    private static final String TAG = "OptimizedClientManager";
    
    // 配置常量
    private static final int MAX_CONCURRENT_CONNECTIONS = 5; // 最大并发连接数
    private static final long CONNECTION_TIMEOUT_MS = 600000; // 10分钟连接超时
    private static final long CLEANUP_INTERVAL_MS = 60000; // 1分钟清理间隔
    
    private final Map<Integer, NetworkClientConnection> mActiveConnections = new ConcurrentHashMap<>();
    private final Map<String, PendingVDRequest> mPendingRequests = new ConcurrentHashMap<>();
    private final Map<String, ClientConnectionPool> mConnectionPools = new ConcurrentHashMap<>();
    private final AtomicInteger mClientIdCounter = new AtomicInteger(1);
    private final ScheduledExecutorService mCleanupExecutor;
    
    public OptimizedClientManager() {
        mCleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ClientManager-Cleanup");
            t.setDaemon(true);
            return t;
        });

        Log.i(TAG, "优化客户端管理器已初始化");
    }

    /**
     * 启动定期清理任务
     */
    public void startCleanupTask() {
        if (OptimizationConfig.ENABLE_PERIODIC_CLEANUP) {
            mCleanupExecutor.scheduleAtFixedRate(this::performCleanup,
                CLEANUP_INTERVAL_MS, CLEANUP_INTERVAL_MS, TimeUnit.MILLISECONDS);
            Log.i(TAG, "定期清理任务已启动");
        }
    }
    
    /**
     * 生成新的客户端ID
     */
    public int generateNewClientId() {
        return mClientIdCounter.getAndIncrement();
    }
    
    /**
     * 检查是否可以接受新连接
     */
    public boolean canAcceptNewConnection() {
        return mActiveConnections.size() < MAX_CONCURRENT_CONNECTIONS;
    }
    
    /**
     * 添加网络连接
     */
    public boolean addNetworkConnection(int clientId, String userIdentifier, String connectionId) {
        if (!canAcceptNewConnection()) {
            Log.w(TAG, "拒绝新连接：已达到最大并发数 " + MAX_CONCURRENT_CONNECTIONS);
            return false;
        }
        
        NetworkClientConnection connection = new NetworkClientConnection(userIdentifier, connectionId);
        mActiveConnections.put(clientId, connection);
        
        // 添加到用户连接池
        mConnectionPools.computeIfAbsent(userIdentifier, k -> new ClientConnectionPool())
                       .addConnection(clientId, connection);
        
        Log.d(TAG, "添加网络连接: ClientID=" + clientId + ", User=" + userIdentifier);
        return true;
    }
    
    /**
     * 移除网络连接
     */
    public void removeNetworkConnection(int clientId) {
        NetworkClientConnection connection = mActiveConnections.remove(clientId);
        if (connection != null) {
            // 从用户连接池中移除
            ClientConnectionPool pool = mConnectionPools.get(connection.userIdentifier);
            if (pool != null) {
                pool.removeConnection(clientId);
                if (pool.isEmpty()) {
                    mConnectionPools.remove(connection.userIdentifier);
                }
            }
            
            Log.d(TAG, "移除网络连接: ClientID=" + clientId);
        }
    }
    
    /**
     * 获取用户的活跃连接
     */
    public @Nullable NetworkClientConnection getUserActiveConnection(String userIdentifier) {
        ClientConnectionPool pool = mConnectionPools.get(userIdentifier);
        return pool != null ? pool.getActiveConnection() : null;
    }
    
    /**
     * 检查显示是否被网络客户端使用
     */
    public boolean isDisplayBeingUsedByNetworkClient(int clientId) {
        return mActiveConnections.containsKey(clientId);
    }
    
    /**
     * 获取显示的网络用户信息
     */
    public @Nullable String getDisplayNetworkUserInfo(int clientId) {
        NetworkClientConnection connection = mActiveConnections.get(clientId);
        if (connection != null) {
            long durationSec = connection.getConnectionDuration() / 1000;
            String shortId = connection.getShortConnectionId();
            return "网络:" + connection.userIdentifier + " (连接" + durationSec + "秒, ID:" + shortId + ")";
        }
        return null;
    }
    
    /**
     * 添加待处理请求
     */
    public void addPendingRequest(String requestId, PendingVDRequest request) {
        mPendingRequests.put(requestId, request);
        Log.d(TAG, "添加待处理请求: " + requestId);
    }
    
    /**
     * 移除待处理请求
     */
    public @Nullable PendingVDRequest removePendingRequest(String requestId) {
        PendingVDRequest request = mPendingRequests.remove(requestId);
        if (request != null) {
            Log.d(TAG, "移除待处理请求: " + requestId);
        }
        return request;
    }
    
    /**
     * 获取待处理请求
     */
    public @Nullable PendingVDRequest getPendingRequest(String requestId) {
        return mPendingRequests.get(requestId);
    }
    
    /**
     * 清理所有连接
     */
    public void clearAllConnections() {
        Log.i(TAG, "清理所有连接: " + mActiveConnections.size() + " 个活跃连接");
        mActiveConnections.clear();
        mConnectionPools.clear();
        mPendingRequests.clear();
    }
    
    /**
     * 获取统计信息
     */
    public ConnectionStats getConnectionStats() {
        return new ConnectionStats(
            mActiveConnections.size(),
            mPendingRequests.size(),
            mConnectionPools.size()
        );
    }
    
    /**
     * 执行定期清理
     */
    private void performCleanup() {
        long currentTime = System.currentTimeMillis();
        
        // 清理超时的连接
        mActiveConnections.entrySet().removeIf(entry -> {
            NetworkClientConnection connection = entry.getValue();
            if (connection.getConnectionDuration() > CONNECTION_TIMEOUT_MS) {
                Log.i(TAG, "清理超时连接: ClientID=" + entry.getKey() + 
                      ", User=" + connection.userIdentifier);
                return true;
            }
            return false;
        });
        
        // 清理空的连接池
        mConnectionPools.entrySet().removeIf(entry -> {
            if (entry.getValue().isEmpty()) {
                Log.d(TAG, "清理空连接池: " + entry.getKey());
                return true;
            }
            return false;
        });
        
        // 清理超时的待处理请求
        mPendingRequests.entrySet().removeIf(entry -> {
            PendingVDRequest request = entry.getValue();
            if ((currentTime - request.getCreationTime()) > CONNECTION_TIMEOUT_MS) {
                Log.i(TAG, "清理超时待处理请求: " + entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        mCleanupExecutor.shutdown();
        try {
            if (!mCleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                mCleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            mCleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        clearAllConnections();
        Log.i(TAG, "优化客户端管理器已关闭");
    }
    
    /**
     * 客户端连接池
     */
    private static class ClientConnectionPool {
        private final Map<Integer, NetworkClientConnection> connections = new ConcurrentHashMap<>();
        
        void addConnection(int clientId, NetworkClientConnection connection) {
            connections.put(clientId, connection);
        }
        
        void removeConnection(int clientId) {
            connections.remove(clientId);
        }
        
        boolean isEmpty() {
            return connections.isEmpty();
        }
        
        @Nullable NetworkClientConnection getActiveConnection() {
            return connections.values().stream().findFirst().orElse(null);
        }
    }
    
    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        public final int activeConnections;
        public final int pendingRequests;
        public final int userPools;
        
        ConnectionStats(int activeConnections, int pendingRequests, int userPools) {
            this.activeConnections = activeConnections;
            this.pendingRequests = pendingRequests;
            this.userPools = userPools;
        }
        
        @Override
        public String toString() {
            return "ConnectionStats{" +
                   "activeConnections=" + activeConnections +
                   ", pendingRequests=" + pendingRequests +
                   ", userPools=" + userPools +
                   '}';
        }
    }
}
