package com.example.virtualdisplaydemo.manager;

import android.content.Context;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;
import com.example.virtualdisplaydemo.config.OptimizationConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 简化版优化虚拟显示管理器
 * 移除复杂的池化和监控功能，专注于基本的内存优化
 */
public class SimpleOptimizedVirtualDisplayManager {
    private static final String TAG = "SimpleOptimizedVDManager";
    private static SimpleOptimizedVirtualDisplayManager sInstance;
    
    private final Context mContext;
    private final DisplayManager mDisplayManager;
    private final Map<Integer, VirtualDisplayContainer> mActiveVirtualDisplays = new ConcurrentHashMap<>();
    
    private SimpleOptimizedVirtualDisplayManager(Context context) {
        mContext = context.getApplicationContext();
        mDisplayManager = (DisplayManager) mContext.getSystemService(Context.DISPLAY_SERVICE);
        Log.i(TAG, "简化版优化虚拟显示管理器已初始化");
    }
    
    public static synchronized SimpleOptimizedVirtualDisplayManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new SimpleOptimizedVirtualDisplayManager(context);
        }
        return sInstance;
    }
    
    /**
     * 创建虚拟显示 - 简化版优化
     */
    public @Nullable VirtualDisplayContainer createVirtualDisplay(
            int internalClientId, 
            @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, 
            @NonNull String initialUserIdentifier, 
            boolean isManual) {
        
        if (mDisplayManager == null || !targetSurface.isValid()) {
            Log.e(TAG, "创建VD的前提条件未满足。InternalID=" + internalClientId);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "DisplayManager或Surface无效");
        }
        
        // 简单的并发检查
        if (!canCreateNewDisplay()) {
            Log.w(TAG, "无法创建新显示器：已达到最大并发数或内存不足");
            
            // 尝试复用现有的手动显示器
            if (!isManual) {
                VirtualDisplayContainer reusable = findReusableManualVirtualDisplay(initialUserIdentifier);
                if (reusable != null) {
                    Log.i(TAG, "复用现有显示器: " + initialUserIdentifier);
                    return reusable;
                }
            }
            
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, 
                "已达到最大并发显示器数量或内存不足");
        }
        
        // 构建虚拟显示参数
        VirtualDisplayParams params = buildVirtualDisplayParams(initialUserIdentifier, internalClientId, isManual);
        
        VirtualDisplay virtualDisplayInstance = null;
        try {
            Log.i(TAG, "正在尝试创建VD: " + params.name + " (手动=" + isManual + ", 大小=" + params.width + "x" + params.height + ")");
            virtualDisplayInstance = mediaProjection.createVirtualDisplay(
                params.name, params.width, params.height, params.densityDpi, 
                params.flags, targetSurface, null, null);
        } catch (SecurityException se) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生SecurityException: " + se.getMessage(), se);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "MediaProjection安全异常: " + se.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生异常: " + e.getMessage(), e);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD时异常: " + e.getMessage());
        }
        
        if (virtualDisplayInstance != null && virtualDisplayInstance.getDisplay() != null) {
            VirtualDisplayContainer container = new VirtualDisplayContainer(
                virtualDisplayInstance, targetSurface, internalClientId, initialUserIdentifier, isManual);
            
            if (container.getDisplayId() == -1) {
                Log.e(TAG, "VD '" + params.name + "' 已创建，但DisplayID无效。释放此VD。");
                virtualDisplayInstance.release();
                return VirtualDisplayContainer.createErrorContainer(
                    internalClientId, initialUserIdentifier, isManual, "获取的DisplayID无效");
            }
            
            mActiveVirtualDisplays.put(internalClientId, container);
            Log.i(TAG, "VD创建成功: " + params.name + " (DisplayID: " + container.getDisplayId() + ")");
            return container;
        } else {
            Log.e(TAG, "创建VD '" + params.name + "' 失败或未能获取Display对象。");
            if (virtualDisplayInstance != null) virtualDisplayInstance.release();
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD失败或Display对象为空");
        }
    }
    
    /**
     * 释放虚拟显示
     */
    public void releaseVirtualDisplay(int internalClientId) {
        VirtualDisplayContainer container = mActiveVirtualDisplays.remove(internalClientId);
        if (container == null) {
            Log.w(TAG, "尝试释放VD (InternalID: " + internalClientId + "), 但未在活动列表中找到。");
            return;
        }
        
        Log.i(TAG, "释放VD: InternalID=" + internalClientId + ", DisplayID=" + container.getDisplayId());
        
        if (container.getVirtualDisplay() != null) {
            try {
                container.getVirtualDisplay().release();
                Log.d(TAG, "VD对象已释放: InternalID=" + internalClientId);
            } catch (Exception e) {
                Log.e(TAG, "释放VD对象时出错: " + e.getMessage(), e);
            }
        }
        
        if (container.getTargetSurface() != null && container.getTargetSurface().isValid()) {
            try {
                container.getTargetSurface().release();
                Log.d(TAG, "Surface已释放: InternalID=" + internalClientId);
            } catch (Exception e) {
                Log.e(TAG, "释放Surface时出错: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 获取虚拟显示容器
     */
    public @Nullable VirtualDisplayContainer getVirtualDisplayContainer(int internalClientId) {
        return mActiveVirtualDisplays.get(internalClientId);
    }
    
    /**
     * 获取所有活跃的虚拟显示
     */
    public Map<Integer, VirtualDisplayContainer> getAllActiveVirtualDisplays() {
        return new ConcurrentHashMap<>(mActiveVirtualDisplays);
    }
    
    /**
     * 获取当前活跃显示器数量
     */
    public int getActiveDisplayCount() {
        return mActiveVirtualDisplays.size();
    }
    
    /**
     * 检查是否可以创建新的显示器
     */
    public boolean canCreateNewDisplay() {
        int maxDisplays = OptimizationConfig.getOptimalMaxDisplays();
        int currentMemoryUsage = OptimizationConfig.getCurrentMemoryUsagePercent();
        
        boolean canCreate = mActiveVirtualDisplays.size() < maxDisplays && 
                           currentMemoryUsage < OptimizationConfig.MEMORY_WARNING_THRESHOLD;
        
        if (!canCreate) {
            Log.w(TAG, "无法创建新显示器: 当前=" + mActiveVirtualDisplays.size() + 
                  "/" + maxDisplays + ", 内存使用率=" + currentMemoryUsage + "%");
        }
        
        return canCreate;
    }
    
    /**
     * 查找可复用的手动虚拟显示
     */
    public @Nullable VirtualDisplayContainer findReusableManualVirtualDisplay(String networkUserIdentifier) {
        VirtualDisplayContainer earliestAvailable = null;
        long earliestTime = Long.MAX_VALUE;
        
        for (VirtualDisplayContainer container : mActiveVirtualDisplays.values()) {
            if (container.isManuallyCreated() && 
                !container.isCurrentlyOccupiedByNetwork() && 
                container.getVirtualDisplay() != null && 
                container.getVirtualDisplay().getDisplay() != null) {
                
                if (container.getCreationTime() < earliestTime) {
                    earliestTime = container.getCreationTime();
                    earliestAvailable = container;
                }
            }
        }
        
        if (earliestAvailable != null) {
            if (earliestAvailable.tryOccupy(networkUserIdentifier)) {
                Log.i(TAG, "找到并成功占用最早的手动VD: InternalID=" + 
                    earliestAvailable.getInternalClientId() + " 给用户 '" + networkUserIdentifier + "'");
                return earliestAvailable;
            } else {
                Log.w(TAG, "找到最早手动VD (InternalID: " + 
                    earliestAvailable.getInternalClientId() + ") 但占用失败。");
            }
        }
        
        Log.i(TAG, "扫描完成：没有找到可供用户 '" + networkUserIdentifier + "' 复用的、空闲且有效的手动VD。");
        return null;
    }
    
    /**
     * 手动清理空闲显示器
     */
    public void cleanupIdleDisplays() {
        long currentTime = System.currentTimeMillis();
        int cleaned = 0;
        
        mActiveVirtualDisplays.entrySet().removeIf(entry -> {
            VirtualDisplayContainer container = entry.getValue();
            if (container.isManuallyCreated() && 
                !container.isCurrentlyOccupiedByNetwork() &&
                (currentTime - container.getLastActivityTime()) > OptimizationConfig.DISPLAY_IDLE_TIMEOUT_MS) {
                
                Log.i(TAG, "清理空闲显示器: InternalID=" + entry.getKey());
                releaseVirtualDisplayInternal(container);
                return true;
            }
            return false;
        });
        
        if (cleaned > 0) {
            Log.i(TAG, "清理了 " + cleaned + " 个空闲显示器");
        }
    }
    
    /**
     * 内部释放显示器方法
     */
    private void releaseVirtualDisplayInternal(VirtualDisplayContainer container) {
        if (container.getVirtualDisplay() != null) {
            try {
                container.getVirtualDisplay().release();
            } catch (Exception e) {
                Log.e(TAG, "释放VD对象时出错: " + e.getMessage(), e);
            }
        }
        
        if (container.getTargetSurface() != null && container.getTargetSurface().isValid()) {
            try {
                container.getTargetSurface().release();
            } catch (Exception e) {
                Log.e(TAG, "释放Surface时出错: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 构建虚拟显示参数
     */
    private VirtualDisplayParams buildVirtualDisplayParams(String userIdentifier, int internalClientId, boolean isManual) {
        String sanitizedId = userIdentifier.replaceAll("[^a-zA-Z0-9_-]", "_");
        String vdName = (isManual ? "ManualVD_" : "NetVD_") + sanitizedId + "_" + 
                       internalClientId + "_" + (System.currentTimeMillis() % 10000);
        
        DisplayMetrics metrics = mContext.getResources().getDisplayMetrics();
        OptimizationConfig.DisplayResolution resolution = OptimizationConfig.getRecommendedResolution();
        int width = Math.max(resolution.width, metrics.widthPixels / 3);
        int height = resolution.height;
        int densityDpi = metrics.densityDpi;
        
        int vdFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            vdFlags |= (1 << 6); /* SECURE FLAG */
        }
        
        return new VirtualDisplayParams(vdName, width, height, densityDpi, vdFlags);
    }
    
    /**
     * 虚拟显示参数内部类
     */
    private static class VirtualDisplayParams {
        final String name;
        final int width;
        final int height;
        final int densityDpi;
        final int flags;
        
        VirtualDisplayParams(String name, int width, int height, int densityDpi, int flags) {
            this.name = name;
            this.width = width;
            this.height = height;
            this.densityDpi = densityDpi;
            this.flags = flags;
        }
    }
}
