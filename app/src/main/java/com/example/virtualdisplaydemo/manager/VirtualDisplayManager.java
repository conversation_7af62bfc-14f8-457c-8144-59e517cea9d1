package com.example.virtualdisplaydemo.manager;

import android.content.Context;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 虚拟显示管理器 - 专门负责虚拟显示的创建、管理和释放
 * 使用单例模式确保全局唯一性
 */
public class VirtualDisplayManager {
    private static final String TAG = "VirtualDisplayManager";
    private static VirtualDisplayManager sInstance;
    
    private final Context mContext;
    private final DisplayManager mDisplayManager;
    private final Map<Integer, VirtualDisplayContainer> mActiveVirtualDisplays = new ConcurrentHashMap<>();
    
    private VirtualDisplayManager(Context context) {
        mContext = context.getApplicationContext();
        mDisplayManager = (DisplayManager) mContext.getSystemService(Context.DISPLAY_SERVICE);
    }
    
    public static synchronized VirtualDisplayManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new VirtualDisplayManager(context);
        }
        return sInstance;
    }
    
    /**
     * 创建虚拟显示
     */
    public @Nullable VirtualDisplayContainer createVirtualDisplay(
            int internalClientId, 
            @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, 
            @NonNull String initialUserIdentifier, 
            boolean isManual) {
        
        if (mDisplayManager == null || !targetSurface.isValid()) {
            Log.e(TAG, "创建VD的前提条件未满足。InternalID=" + internalClientId);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "DisplayManager或Surface无效");
        }
        
        // 构建虚拟显示参数
        VirtualDisplayParams params = buildVirtualDisplayParams(initialUserIdentifier, internalClientId, isManual);
        
        VirtualDisplay virtualDisplayInstance = null;
        try {
            Log.i(TAG, "正在尝试创建VD: " + params.name + " (手动=" + isManual + ", 大小=" + params.width + "x" + params.height + ")");
            virtualDisplayInstance = mediaProjection.createVirtualDisplay(
                params.name, params.width, params.height, params.densityDpi, 
                params.flags, targetSurface, null, null);
        } catch (SecurityException se) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生SecurityException: " + se.getMessage(), se);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "MediaProjection安全异常: " + se.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生异常: " + e.getMessage(), e);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD时异常: " + e.getMessage());
        }
        
        if (virtualDisplayInstance != null && virtualDisplayInstance.getDisplay() != null) {
            VirtualDisplayContainer container = new VirtualDisplayContainer(
                virtualDisplayInstance, targetSurface, internalClientId, initialUserIdentifier, isManual);
            
            if (container.getDisplayId() == -1) {
                Log.e(TAG, "VD '" + params.name + "' 已创建，但DisplayID无效。释放此VD。");
                virtualDisplayInstance.release();
                return VirtualDisplayContainer.createErrorContainer(
                    internalClientId, initialUserIdentifier, isManual, "获取的DisplayID无效");
            }
            
            mActiveVirtualDisplays.put(internalClientId, container);
            Log.i(TAG, "VD创建成功: " + params.name + " (DisplayID: " + container.getDisplayId() + ")");
            return container;
        } else {
            Log.e(TAG, "创建VD '" + params.name + "' 失败或未能获取Display对象。");
            if (virtualDisplayInstance != null) virtualDisplayInstance.release();
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD失败或Display对象为空");
        }
    }
    
    /**
     * 释放虚拟显示
     */
    public void releaseVirtualDisplay(int internalClientId) {
        VirtualDisplayContainer container = mActiveVirtualDisplays.remove(internalClientId);
        if (container == null) {
            Log.w(TAG, "尝试释放VD (InternalID: " + internalClientId + "), 但未在活动列表中找到。");
            return;
        }
        
        Log.i(TAG, "释放VD: InternalID=" + internalClientId + ", DisplayID=" + container.getDisplayId());
        
        if (container.getVirtualDisplay() != null) {
            try {
                container.getVirtualDisplay().release();
                Log.d(TAG, "VD对象已释放: InternalID=" + internalClientId);
            } catch (Exception e) {
                Log.e(TAG, "释放VD对象时出错: " + e.getMessage(), e);
            }
        }
        
        if (container.getTargetSurface() != null && container.getTargetSurface().isValid()) {
            try {
                container.getTargetSurface().release();
                Log.d(TAG, "Surface已释放: InternalID=" + internalClientId);
            } catch (Exception e) {
                Log.e(TAG, "释放Surface时出错: " + e.getMessage(), e);
            }
        }
    }
    
    /**
     * 获取虚拟显示容器
     */
    public @Nullable VirtualDisplayContainer getVirtualDisplayContainer(int internalClientId) {
        return mActiveVirtualDisplays.get(internalClientId);
    }
    
    /**
     * 获取所有活跃的虚拟显示
     */
    public Map<Integer, VirtualDisplayContainer> getAllActiveVirtualDisplays() {
        return new ConcurrentHashMap<>(mActiveVirtualDisplays);
    }
    
    /**
     * 查找可复用的手动虚拟显示
     */
    public @Nullable VirtualDisplayContainer findReusableManualVirtualDisplay(String networkUserIdentifier) {
        VirtualDisplayContainer earliestAvailable = null;
        long earliestTime = Long.MAX_VALUE;
        
        for (VirtualDisplayContainer container : mActiveVirtualDisplays.values()) {
            if (container.isManuallyCreated() && 
                !container.isCurrentlyOccupiedByNetwork() && 
                container.getVirtualDisplay() != null && 
                container.getVirtualDisplay().getDisplay() != null) {
                
                if (container.getCreationTime() < earliestTime) {
                    earliestTime = container.getCreationTime();
                    earliestAvailable = container;
                }
            }
        }
        
        if (earliestAvailable != null) {
            if (earliestAvailable.tryOccupy(networkUserIdentifier)) {
                Log.i(TAG, "找到并成功占用最早的手动VD: InternalID=" + 
                    earliestAvailable.getInternalClientId() + " 给用户 '" + networkUserIdentifier + "'");
                return earliestAvailable;
            } else {
                Log.w(TAG, "找到最早手动VD (InternalID: " + 
                    earliestAvailable.getInternalClientId() + ") 但占用失败。");
            }
        }
        
        Log.i(TAG, "扫描完成：没有找到可供用户 '" + networkUserIdentifier + "' 复用的、空闲且有效的手动VD。");
        return null;
    }
    
    /**
     * 构建虚拟显示参数
     */
    private VirtualDisplayParams buildVirtualDisplayParams(String userIdentifier, int internalClientId, boolean isManual) {
        String sanitizedId = userIdentifier.replaceAll("[^a-zA-Z0-9_-]", "_");
        String vdName = (isManual ? "ManualVD_" : "NetVD_") + sanitizedId + "_" + 
                       internalClientId + "_" + (System.currentTimeMillis() % 10000);
        
        DisplayMetrics metrics = mContext.getResources().getDisplayMetrics();
        int width = Math.max(1280, metrics.widthPixels / 2);
        int height = Math.max(720, (width * 9) / 16);
        int densityDpi = metrics.densityDpi;
        
        int vdFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            vdFlags |= (1 << 6); /* SECURE FLAG */
        }
        
        return new VirtualDisplayParams(vdName, width, height, densityDpi, vdFlags);
    }
    
    /**
     * 虚拟显示参数内部类
     */
    private static class VirtualDisplayParams {
        final String name;
        final int width;
        final int height;
        final int densityDpi;
        final int flags;
        
        VirtualDisplayParams(String name, int width, int height, int densityDpi, int flags) {
            this.name = name;
            this.width = width;
            this.height = height;
            this.densityDpi = densityDpi;
            this.flags = flags;
        }
    }
}
