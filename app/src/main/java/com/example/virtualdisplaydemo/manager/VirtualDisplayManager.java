package com.example.virtualdisplaydemo.manager;

import android.content.Context;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.os.Build;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;
import com.example.virtualdisplaydemo.config.OptimizationConfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 虚拟显示管理器 - 专门负责虚拟显示的创建、管理和释放
 * 使用单例模式确保全局唯一性
 * 优化版本：支持显示器池化管理和内存优化
 */
public class VirtualDisplayManager {
    private static final String TAG = "VirtualDisplayManager";
    private static VirtualDisplayManager sInstance;

    // 配置常量 - 使用OptimizationConfig中的值
    private static final int MAX_CONCURRENT_DISPLAYS = OptimizationConfig.MAX_CONCURRENT_DISPLAYS;
    private static final long IDLE_TIMEOUT_MS = OptimizationConfig.DISPLAY_IDLE_TIMEOUT_MS;
    private static final long MEMORY_CHECK_INTERVAL_MS = OptimizationConfig.MEMORY_CHECK_INTERVAL_MS;

    private final Context mContext;
    private final DisplayManager mDisplayManager;
    private final Map<Integer, VirtualDisplayContainer> mActiveVirtualDisplays = new ConcurrentHashMap<>();
    private final VirtualDisplayPool mDisplayPool;
    private final MemoryMonitor mMemoryMonitor;
    
    private VirtualDisplayManager(Context context) {
        mContext = context.getApplicationContext();
        mDisplayManager = (DisplayManager) mContext.getSystemService(Context.DISPLAY_SERVICE);
        mDisplayPool = new VirtualDisplayPool();
        mMemoryMonitor = new MemoryMonitor();
        // 延迟启动内存监控，避免在构造函数中启动线程
    }
    
    public static synchronized VirtualDisplayManager getInstance(Context context) {
        if (sInstance == null) {
            sInstance = new VirtualDisplayManager(context);
        }
        return sInstance;
    }

    /**
     * 启动内存监控 - 在服务启动后调用
     */
    public void startMemoryMonitoring() {
        if (mMemoryMonitor != null) {
            mMemoryMonitor.startMonitoring();
        }
    }

    /**
     * 停止内存监控 - 在服务销毁时调用
     */
    public void stopMemoryMonitoring() {
        if (mMemoryMonitor != null) {
            mMemoryMonitor.stopMonitoring();
        }
    }
    
    /**
     * 创建虚拟显示 - 优化版本，支持池化和内存管理
     */
    public @Nullable VirtualDisplayContainer createVirtualDisplay(
            int internalClientId,
            @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection,
            @NonNull String initialUserIdentifier,
            boolean isManual) {

        if (mDisplayManager == null || !targetSurface.isValid()) {
            Log.e(TAG, "创建VD的前提条件未满足。InternalID=" + internalClientId);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "DisplayManager或Surface无效");
        }

        // 检查是否可以创建新的显示器
        if (!canCreateNewDisplay()) {
            Log.w(TAG, "无法创建新显示器：已达到最大并发数或内存不足");

            // 尝试从池中获取可复用的显示器
            if (!isManual) {
                VirtualDisplayContainer reusable = mDisplayPool.getReusableDisplay(initialUserIdentifier);
                if (reusable != null) {
                    Log.i(TAG, "使用池化显示器替代新建: " + initialUserIdentifier);
                    return reusable;
                }
            }

            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual,
                "已达到最大并发显示器数量或内存不足");
        }
        
        // 构建虚拟显示参数
        VirtualDisplayParams params = buildVirtualDisplayParams(initialUserIdentifier, internalClientId, isManual);
        
        VirtualDisplay virtualDisplayInstance = null;
        try {
            Log.i(TAG, "正在尝试创建VD: " + params.name + " (手动=" + isManual + ", 大小=" + params.width + "x" + params.height + ")");
            virtualDisplayInstance = mediaProjection.createVirtualDisplay(
                params.name, params.width, params.height, params.densityDpi, 
                params.flags, targetSurface, null, null);
        } catch (SecurityException se) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生SecurityException: " + se.getMessage(), se);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "MediaProjection安全异常: " + se.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "创建VD'" + params.name + "' 时发生异常: " + e.getMessage(), e);
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD时异常: " + e.getMessage());
        }
        
        if (virtualDisplayInstance != null && virtualDisplayInstance.getDisplay() != null) {
            VirtualDisplayContainer container = new VirtualDisplayContainer(
                virtualDisplayInstance, targetSurface, internalClientId, initialUserIdentifier, isManual);
            
            if (container.getDisplayId() == -1) {
                Log.e(TAG, "VD '" + params.name + "' 已创建，但DisplayID无效。释放此VD。");
                virtualDisplayInstance.release();
                return VirtualDisplayContainer.createErrorContainer(
                    internalClientId, initialUserIdentifier, isManual, "获取的DisplayID无效");
            }
            
            mActiveVirtualDisplays.put(internalClientId, container);
            Log.i(TAG, "VD创建成功: " + params.name + " (DisplayID: " + container.getDisplayId() + ")");
            return container;
        } else {
            Log.e(TAG, "创建VD '" + params.name + "' 失败或未能获取Display对象。");
            if (virtualDisplayInstance != null) virtualDisplayInstance.release();
            return VirtualDisplayContainer.createErrorContainer(
                internalClientId, initialUserIdentifier, isManual, "创建VD失败或Display对象为空");
        }
    }
    
    /**
     * 释放虚拟显示 - 优化版本，支持池化
     */
    public void releaseVirtualDisplay(int internalClientId) {
        VirtualDisplayContainer container = mActiveVirtualDisplays.get(internalClientId);
        if (container == null) {
            Log.w(TAG, "尝试释放VD (InternalID: " + internalClientId + "), 但未在活动列表中找到。");
            return;
        }

        Log.i(TAG, "释放VD: InternalID=" + internalClientId + ", DisplayID=" + container.getDisplayId());

        // 如果是手动创建的显示器，尝试放入池中复用
        if (container.isManuallyCreated() && !container.hasError()) {
            container.releaseOccupation();
            String userIdentifier = container.getOccupyingNetworkUser();
            if (userIdentifier != null) {
                mDisplayPool.returnToPool(userIdentifier, internalClientId);
                Log.i(TAG, "手动VD已返回池中等待复用: InternalID=" + internalClientId);
                return;
            }
        }

        // 完全释放显示器
        mActiveVirtualDisplays.remove(internalClientId);
        releaseVirtualDisplayInternal(container);
    }
    
    /**
     * 获取虚拟显示容器
     */
    public @Nullable VirtualDisplayContainer getVirtualDisplayContainer(int internalClientId) {
        return mActiveVirtualDisplays.get(internalClientId);
    }
    
    /**
     * 获取所有活跃的虚拟显示
     */
    public Map<Integer, VirtualDisplayContainer> getAllActiveVirtualDisplays() {
        return new ConcurrentHashMap<>(mActiveVirtualDisplays);
    }
    
    /**
     * 查找可复用的手动虚拟显示
     */
    public @Nullable VirtualDisplayContainer findReusableManualVirtualDisplay(String networkUserIdentifier) {
        VirtualDisplayContainer earliestAvailable = null;
        long earliestTime = Long.MAX_VALUE;
        
        for (VirtualDisplayContainer container : mActiveVirtualDisplays.values()) {
            if (container.isManuallyCreated() && 
                !container.isCurrentlyOccupiedByNetwork() && 
                container.getVirtualDisplay() != null && 
                container.getVirtualDisplay().getDisplay() != null) {
                
                if (container.getCreationTime() < earliestTime) {
                    earliestTime = container.getCreationTime();
                    earliestAvailable = container;
                }
            }
        }
        
        if (earliestAvailable != null) {
            if (earliestAvailable.tryOccupy(networkUserIdentifier)) {
                Log.i(TAG, "找到并成功占用最早的手动VD: InternalID=" + 
                    earliestAvailable.getInternalClientId() + " 给用户 '" + networkUserIdentifier + "'");
                return earliestAvailable;
            } else {
                Log.w(TAG, "找到最早手动VD (InternalID: " + 
                    earliestAvailable.getInternalClientId() + ") 但占用失败。");
            }
        }
        
        Log.i(TAG, "扫描完成：没有找到可供用户 '" + networkUserIdentifier + "' 复用的、空闲且有效的手动VD。");
        return null;
    }
    
    /**
     * 构建虚拟显示参数
     */
    private VirtualDisplayParams buildVirtualDisplayParams(String userIdentifier, int internalClientId, boolean isManual) {
        String sanitizedId = userIdentifier.replaceAll("[^a-zA-Z0-9_-]", "_");
        String vdName = (isManual ? "ManualVD_" : "NetVD_") + sanitizedId + "_" + 
                       internalClientId + "_" + (System.currentTimeMillis() % 10000);
        
        DisplayMetrics metrics = mContext.getResources().getDisplayMetrics();
        OptimizationConfig.DisplayResolution resolution = OptimizationConfig.getRecommendedResolution();
        int width = Math.max(resolution.width, metrics.widthPixels / 3); // 使用更保守的分辨率
        int height = resolution.height;
        int densityDpi = metrics.densityDpi;
        
        int vdFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            vdFlags |= (1 << 6); /* SECURE FLAG */
        }
        
        return new VirtualDisplayParams(vdName, width, height, densityDpi, vdFlags);
    }
    
    /**
     * 获取当前活跃显示器数量
     */
    public int getActiveDisplayCount() {
        return mActiveVirtualDisplays.size();
    }

    /**
     * 检查是否可以创建新的显示器 - 使用动态配置
     */
    public boolean canCreateNewDisplay() {
        int maxDisplays = OptimizationConfig.getOptimalMaxDisplays();
        return mActiveVirtualDisplays.size() < maxDisplays &&
               mMemoryMonitor.hasAvailableMemory() &&
               !OptimizationConfig.shouldRejectNewConnections();
    }

    /**
     * 清理空闲的显示器
     */
    public void cleanupIdleDisplays() {
        long currentTime = System.currentTimeMillis();
        mActiveVirtualDisplays.entrySet().removeIf(entry -> {
            VirtualDisplayContainer container = entry.getValue();
            if (container.isManuallyCreated() &&
                !container.isCurrentlyOccupiedByNetwork() &&
                (currentTime - container.getLastActivityTime()) > IDLE_TIMEOUT_MS) {

                Log.i(TAG, "清理空闲显示器: InternalID=" + entry.getKey());
                releaseVirtualDisplayInternal(container);
                return true;
            }
            return false;
        });
    }

    /**
     * 内部释放显示器方法
     */
    private void releaseVirtualDisplayInternal(VirtualDisplayContainer container) {
        if (container.getVirtualDisplay() != null) {
            try {
                container.getVirtualDisplay().release();
            } catch (Exception e) {
                Log.e(TAG, "释放VD对象时出错: " + e.getMessage(), e);
            }
        }

        if (container.getTargetSurface() != null && container.getTargetSurface().isValid()) {
            try {
                container.getTargetSurface().release();
            } catch (Exception e) {
                Log.e(TAG, "释放Surface时出错: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 虚拟显示参数内部类
     */
    private static class VirtualDisplayParams {
        final String name;
        final int width;
        final int height;
        final int densityDpi;
        final int flags;

        VirtualDisplayParams(String name, int width, int height, int densityDpi, int flags) {
            this.name = name;
            this.width = width;
            this.height = height;
            this.densityDpi = densityDpi;
            this.flags = flags;
        }
    }

    /**
     * 虚拟显示器池管理类
     */
    private class VirtualDisplayPool {
        private final Map<String, PooledDisplayInfo> mPooledDisplays = new ConcurrentHashMap<>();

        /**
         * 尝试从池中获取可复用的显示器
         */
        public VirtualDisplayContainer getReusableDisplay(String userIdentifier) {
            // 首先尝试找到完全匹配的显示器
            PooledDisplayInfo pooledInfo = mPooledDisplays.get(userIdentifier);
            if (pooledInfo != null && pooledInfo.isAvailable()) {
                VirtualDisplayContainer container = mActiveVirtualDisplays.get(pooledInfo.internalClientId);
                if (container != null && container.tryOccupy(userIdentifier)) {
                    pooledInfo.markInUse();
                    Log.i(TAG, "从池中复用显示器: " + userIdentifier);
                    return container;
                }
            }

            // 如果没有完全匹配的，尝试找到任何可用的手动显示器
            return findReusableManualVirtualDisplay(userIdentifier);
        }

        /**
         * 将显示器返回到池中
         */
        public void returnToPool(String userIdentifier, int internalClientId) {
            PooledDisplayInfo pooledInfo = new PooledDisplayInfo(internalClientId);
            mPooledDisplays.put(userIdentifier, pooledInfo);
            Log.d(TAG, "显示器返回到池中: " + userIdentifier);
        }

        /**
         * 清理池中的无效显示器
         */
        public void cleanup() {
            mPooledDisplays.entrySet().removeIf(entry -> {
                PooledDisplayInfo info = entry.getValue();
                VirtualDisplayContainer container = mActiveVirtualDisplays.get(info.internalClientId);
                return container == null || container.hasError();
            });
        }
    }

    /**
     * 池化显示器信息
     */
    private static class PooledDisplayInfo {
        final int internalClientId;
        final long poolTime;
        private boolean inUse;

        PooledDisplayInfo(int internalClientId) {
            this.internalClientId = internalClientId;
            this.poolTime = System.currentTimeMillis();
            this.inUse = false;
        }

        boolean isAvailable() {
            return !inUse;
        }

        void markInUse() {
            this.inUse = true;
        }

        void markAvailable() {
            this.inUse = false;
        }
    }

    /**
     * 内存监控器
     */
    private class MemoryMonitor {
        private Thread mMonitorThread;
        private volatile boolean mIsRunning = false;
        private final Runtime mRuntime = Runtime.getRuntime();

        void startMonitoring() {
            if (mIsRunning) return;

            mIsRunning = true;
            mMonitorThread = new Thread(this::monitorLoop, "MemoryMonitor");
            mMonitorThread.setDaemon(true);
            mMonitorThread.start();
            Log.i(TAG, "内存监控器已启动");
        }

        void stopMonitoring() {
            mIsRunning = false;
            if (mMonitorThread != null) {
                mMonitorThread.interrupt();
            }
        }

        boolean hasAvailableMemory() {
            return !OptimizationConfig.shouldAggressiveCleanup();
        }

        private void monitorLoop() {
            try {
                while (mIsRunning && !Thread.currentThread().isInterrupted()) {
                    Thread.sleep(MEMORY_CHECK_INTERVAL_MS);

                    if (OptimizationConfig.shouldAggressiveCleanup()) {
                        int memoryUsage = OptimizationConfig.getCurrentMemoryUsagePercent();
                        Log.w(TAG, "内存使用率过高(" + memoryUsage + "%)，开始清理空闲显示器");
                        cleanupIdleDisplays();
                        mDisplayPool.cleanup();

                        // 强制垃圾回收（如果启用）
                        if (OptimizationConfig.ENABLE_AUTO_GC) {
                            System.gc();
                        }
                    }
                }
            } catch (InterruptedException e) {
                Log.i(TAG, "内存监控线程被中断");
                Thread.currentThread().interrupt();
            }
        }
    }
}
