package com.example.virtualdisplaydemo.manager;

import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;

import com.example.virtualdisplaydemo.strategy.AppLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.IntentLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.AdbLaunchStrategy;

import java.io.PrintWriter;

/**
 * 应用启动管理器 - 使用策略模式处理不同的应用启动方式
 */
public class AppLaunchManager {
    private static final String TAG = "AppLaunchManager";
    
    private final Context mContext;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    
    // 启动策略
    private final AppLaunchStrategy mIntentStrategy;
    private final AppLaunchStrategy mAdbStrategy;
    
    public AppLaunchManager(Context context, Handler serviceHandler, Handler mainThreadHandler) {
        mContext = context;
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
        
        // 初始化策略
        mIntentStrategy = new IntentLaunchStrategy(context, serviceHandler, mainThreadHandler);
        mAdbStrategy = new AdbLaunchStrategy(serviceHandler);
    }
    
    /**
     * 启动应用到指定显示器
     */
    public void launchAppOnDisplay(@NonNull String packageName, 
                                  @NonNull String activityName, 
                                  @NonNull String displayId, 
                                  @NonNull String requestId,
                                  @NonNull String connectionId,
                                  PrintWriter writer) {
        
        Log.i(TAG, "ConnID: " + connectionId + " - 尝试启动应用到显示器: " + 
              packageName + "/" + activityName + " -> Display " + displayId);
        
        // 首先尝试Intent方式
        mIntentStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId, 
            new AppLaunchStrategy.LaunchCallback() {
                @Override
                public void onLaunchSuccess(String requestId) {
                    if (writer != null && !writer.checkError()) {
                        mServiceHandler.post(() -> {
                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":OK:IntentSent");
                        });
                    }
                }
                
                @Override
                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                    if (canRetryWithAdb) {
                        Log.i(TAG, "Intent启动失败，尝试ADB方式: " + reason);
                        // 尝试ADB方式
                        mAdbStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId,
                            new AppLaunchStrategy.LaunchCallback() {
                                @Override
                                public void onLaunchSuccess(String requestId) {
                                    // ADB策略会直接返回命令给客户端
                                }
                                
                                @Override
                                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                                    if (writer != null && !writer.checkError()) {
                                        mServiceHandler.post(() -> {
                                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                                          sanitizeErrorMessage(reason));
                                        });
                                    }
                                }
                            }, writer);
                    } else {
                        if (writer != null && !writer.checkError()) {
                            mServiceHandler.post(() -> {
                                writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                              sanitizeErrorMessage(reason));
                            });
                        }
                    }
                }
            }, writer);
    }
    
    /**
     * 清理错误消息中的特殊字符
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) return "null";
        return message.replaceAll(":", "_").replaceAll("\n", " ").replaceAll("\r", " ");
    }
    
    /**
     * 检查应用是否正在运行 - 最实用的版本
     *
     * 设计原则：
     * 1. 简单可靠，不依赖复杂的系统命令
     * 2. 应用不运行是正常情况，不是错误
     * 3. 检测失败时安全返回，不抛出异常
     */
    public boolean isAppRunning(String packageName) {
        try {
            // 首先检查应用是否已安装
            if (!isAppInstalled(packageName)) {
                Log.d(TAG, "应用 " + packageName + " 未安装，返回false");
                return false;
            }

            // 使用ActivityManager检查进程（最可靠且权限友好的方法）
            boolean processExists = checkAppProcessSimple(packageName);

            Log.d(TAG, "应用 " + packageName + " 运行状态: " + processExists);
            return processExists;

        } catch (Exception e) {
            Log.w(TAG, "检查应用运行状态时出错: " + e.getMessage());
            // 出错时假设应用仍在运行，避免误断开
            // 但如果是特定的应用不存在错误，则返回false
            if (e.getMessage() != null && e.getMessage().contains("not found")) {
                Log.i(TAG, "应用 " + packageName + " 确实不存在，返回false");
                return false;
            }
            return true;
        }
    }
    
    /**
     * 检查应用进程是否存在 - 简化可靠版本
     */
    private boolean checkAppProcessSimple(String packageName) {
        try {
            // 使用ActivityManager检查运行中的应用（最可靠的方法）
            android.app.ActivityManager activityManager =
                (android.app.ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);

            if (activityManager != null) {
                java.util.List<android.app.ActivityManager.RunningAppProcessInfo> runningProcesses =
                    activityManager.getRunningAppProcesses();

                if (runningProcesses != null) {
                    for (android.app.ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                        // 检查进程名是否匹配（可能是包名或包名:子进程）
                        if (processInfo.processName.equals(packageName) ||
                            processInfo.processName.startsWith(packageName + ":")) {
                            Log.d(TAG, "找到应用进程: " + packageName +
                                  " (进程名: " + processInfo.processName + ", PID: " + processInfo.pid + ")");
                            return true;
                        }
                    }
                }
            }

            Log.d(TAG, "未找到应用进程: " + packageName);
            return false;

        } catch (Exception e) {
            Log.w(TAG, "检查应用进程时出错: " + e.getMessage());
            // 出错时返回false，这样更安全
            return false;
        }
    }
    


    /**
     * 检查应用是否已安装
     */
    public boolean isAppInstalled(String packageName) {
        try {
            android.content.pm.PackageManager packageManager = mContext.getPackageManager();
            packageManager.getPackageInfo(packageName, 0);
            return true;
        } catch (android.content.pm.PackageManager.NameNotFoundException e) {
            Log.d(TAG, "应用 " + packageName + " 未安装");
            return false;
        } catch (Exception e) {
            Log.w(TAG, "检查应用安装状态时出错: " + e.getMessage());
            return true; // 出错时假设已安装
        }
    }
}
