package com.example.virtualdisplaydemo.manager;

import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;

import com.example.virtualdisplaydemo.strategy.AppLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.IntentLaunchStrategy;
import com.example.virtualdisplaydemo.strategy.AdbLaunchStrategy;

import java.io.PrintWriter;

/**
 * 应用启动管理器 - 使用策略模式处理不同的应用启动方式
 */
public class AppLaunchManager {
    private static final String TAG = "AppLaunchManager";
    
    private final Context mContext;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    
    // 启动策略
    private final AppLaunchStrategy mIntentStrategy;
    private final AppLaunchStrategy mAdbStrategy;
    
    public AppLaunchManager(Context context, Handler serviceHandler, Handler mainThreadHandler) {
        mContext = context;
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
        
        // 初始化策略
        mIntentStrategy = new IntentLaunchStrategy(context, serviceHandler, mainThreadHandler);
        mAdbStrategy = new AdbLaunchStrategy(serviceHandler);
    }
    
    /**
     * 启动应用到指定显示器
     */
    public void launchAppOnDisplay(@NonNull String packageName, 
                                  @NonNull String activityName, 
                                  @NonNull String displayId, 
                                  @NonNull String requestId,
                                  @NonNull String connectionId,
                                  PrintWriter writer) {
        
        Log.i(TAG, "ConnID: " + connectionId + " - 尝试启动应用到显示器: " + 
              packageName + "/" + activityName + " -> Display " + displayId);
        
        // 首先尝试Intent方式
        mIntentStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId, 
            new AppLaunchStrategy.LaunchCallback() {
                @Override
                public void onLaunchSuccess(String requestId) {
                    if (writer != null && !writer.checkError()) {
                        mServiceHandler.post(() -> {
                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":OK:IntentSent");
                        });
                    }
                }
                
                @Override
                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                    if (canRetryWithAdb) {
                        Log.i(TAG, "Intent启动失败，尝试ADB方式: " + reason);
                        // 尝试ADB方式
                        mAdbStrategy.launchApp(packageName, activityName, displayId, requestId, connectionId,
                            new AppLaunchStrategy.LaunchCallback() {
                                @Override
                                public void onLaunchSuccess(String requestId) {
                                    // ADB策略会直接返回命令给客户端
                                }
                                
                                @Override
                                public void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb) {
                                    if (writer != null && !writer.checkError()) {
                                        mServiceHandler.post(() -> {
                                            writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                                          sanitizeErrorMessage(reason));
                                        });
                                    }
                                }
                            }, writer);
                    } else {
                        if (writer != null && !writer.checkError()) {
                            mServiceHandler.post(() -> {
                                writer.println("APP_LAUNCH_STATUS:" + requestId + ":ERROR:" + 
                                              sanitizeErrorMessage(reason));
                            });
                        }
                    }
                }
            }, writer);
    }
    
    /**
     * 清理错误消息中的特殊字符
     */
    private String sanitizeErrorMessage(String message) {
        if (message == null) return "null";
        return message.replaceAll(":", "_").replaceAll("\n", " ").replaceAll("\r", " ");
    }
    
    /**
     * 检查应用是否正在运行 - 优化版本
     */
    public boolean isAppRunning(String packageName) {
        try {
            // 方法1: 检查应用进程是否存在（最可靠的方法）
            boolean processExists = checkAppProcess(packageName);

            // 如果进程存在，直接认为应用在运行
            if (processExists) {
                Log.d(TAG, "应用 " + packageName + " 运行状态: true (进程存在)");
                return true;
            }

            // 方法2: 检查应用是否在Activity栈中
            boolean inActivityStack = checkAppInActivityStack(packageName);

            Log.d(TAG, "应用 " + packageName + " 运行状态: " + inActivityStack +
                    " (进程:" + processExists + ", 活动栈:" + inActivityStack + ")");
            return inActivityStack;

        } catch (Exception e) {
            Log.w(TAG, "检查应用运行状态时出错: " + e.getMessage());
            return true; // 出错时假设应用仍在运行，避免误断开
        }
    }
    
    /**
     * 检查应用进程是否存在 - 使用多种方法
     */
    private boolean checkAppProcess(String packageName) {
        try {
            // 方法1: 使用ActivityManager检查运行中的应用
            android.app.ActivityManager activityManager =
                (android.app.ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);

            if (activityManager != null) {
                java.util.List<android.app.ActivityManager.RunningAppProcessInfo> runningProcesses =
                    activityManager.getRunningAppProcesses();

                if (runningProcesses != null) {
                    for (android.app.ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                        if (processInfo.processName.equals(packageName)) {
                            Log.d(TAG, "通过ActivityManager找到应用进程: " + packageName +
                                  " (PID: " + processInfo.pid + ")");
                            return true;
                        }
                    }
                }
            }

            // 方法2: 使用ps命令作为备用方法
            Process process = Runtime.getRuntime().exec(new String[]{"sh", "-c", "ps | grep " + packageName});
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            String line;
            boolean processFound = false;

            while ((line = reader.readLine()) != null) {
                if (line.contains(packageName) && !line.contains("grep")) {
                    processFound = true;
                    Log.d(TAG, "通过ps命令找到应用进程 " + packageName + ": " + line.trim());
                    break;
                }
            }

            reader.close();
            process.waitFor();

            return processFound;
        } catch (Exception e) {
            Log.w(TAG, "检查应用进程时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查应用是否在Activity栈中 - 使用更可靠的方法
     */
    private boolean checkAppInActivityStack(String packageName) {
        // 直接使用备用方法，因为grep -c在应用内部可能有权限问题
        return checkAppInActivityStackFallback(packageName);
    }

    /**
     * Activity栈检测的备用方法 - 优化版本
     */
    private boolean checkAppInActivityStackFallback(String packageName) {
        try {
            Process process = Runtime.getRuntime().exec(new String[]{"sh", "-c", "dumpsys activity activities"});
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            String line;
            int packageLineCount = 0;
            int totalLines = 0;
            boolean foundApp = false;

            while ((line = reader.readLine()) != null) {
                totalLines++;
                if (line.contains(packageName)) {
                    packageLineCount++;
                    Log.d(TAG, "找到包含包名的行 " + packageLineCount + ": " + line.trim());

                    // 只要找到包名就认为应用在运行，立即返回
                    foundApp = true;
                    break;
                }

                // 限制读取行数，避免过长的等待
                if (totalLines > 5000) {
                    Log.w(TAG, "Activity栈输出过长，停止检测");
                    break;
                }
            }

            reader.close();
            process.waitFor();

            Log.d(TAG, "Activity栈检测完成: " + packageName +
                  ", 总行数: " + totalLines +
                  ", 包含包名的行数: " + packageLineCount +
                  ", 找到应用: " + foundApp);

            return foundApp;

        } catch (Exception e) {
            Log.w(TAG, "Activity栈检测时出错: " + e.getMessage());
            return false;
        }
    }
}
