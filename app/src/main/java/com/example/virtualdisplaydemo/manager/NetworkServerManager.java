package com.example.virtualdisplaydemo.manager;

import android.os.Handler;
import android.util.Log;

import com.example.virtualdisplaydemo.handler.ClientSocketHandler;
import com.example.virtualdisplaydemo.callback.ActivityCallback;
import com.example.virtualdisplaydemo.model.PendingVDRequest;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeUnit;

/**
 * 网络服务器管理器 - 专门负责TCP服务器的管理和客户端连接处理
 */
public class NetworkServerManager {
    private static final String TAG = "NetworkServerManager";
    public static final int SERVER_PORT = 33012;
    
    private ServerSocket mServerSocket;
    private ExecutorService mClientHandlingExecutor;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    private final Set<ClientSocketHandler> mActiveClientHandlers = Collections.synchronizedSet(new HashSet<>());
    
    // 依赖的管理器
    private final VirtualDisplayManager mVirtualDisplayManager;
    private final ClientConnectionManager mConnectionManager;
    private final AppLaunchManager mAppLaunchManager;
    
    // 回调
    private ActivityCallback mActivityCallback;
    private final Object mActivityCallbackLock = new Object();
    
    public NetworkServerManager(Handler serviceHandler,
                               Handler mainThreadHandler,
                               VirtualDisplayManager virtualDisplayManager,
                               ClientConnectionManager connectionManager,
                               AppLaunchManager appLaunchManager) {
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
        mVirtualDisplayManager = virtualDisplayManager;
        mConnectionManager = connectionManager;
        mAppLaunchManager = appLaunchManager;
        mClientHandlingExecutor = Executors.newCachedThreadPool();
    }
    
    /**
     * 启动服务器监听
     */
    public void startServerListener() {
        mServiceHandler.post(() -> {
            try {
                mServerSocket = new ServerSocket(SERVER_PORT);
                Log.i(TAG, "TCP服务器已在端口 " + SERVER_PORT + " 上开始监听...");
                
                while (!Thread.currentThread().isInterrupted() && 
                       mServerSocket != null && !mServerSocket.isClosed()) {
                    try {
                        Socket clientSocket = mServerSocket.accept();
                        Log.i(TAG, "客户端已连接，来自: " + clientSocket.getInetAddress());
                        
                        ClientSocketHandler handler = new ClientSocketHandler(
                            clientSocket, 
                            mVirtualDisplayManager,
                            mConnectionManager,
                            mAppLaunchManager,
                            this
                        );
                        
                        mActiveClientHandlers.add(handler);
                        mClientHandlingExecutor.submit(handler);
                        
                    } catch (SocketException se) {
                        if (mServerSocket != null && mServerSocket.isClosed()) {
                            Log.i(TAG, "ServerSocket已关闭，正常退出监听循环。");
                            break;
                        }
                        Log.e(TAG, "监听循环中发生SocketException: " + se.getMessage());
                    } catch (IOException ioe) {
                        Log.e(TAG, "服务器监听循环中发生IOException: " + ioe.getMessage(), ioe);
                        try {
                            TimeUnit.SECONDS.sleep(1);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            } catch (IOException e) {
                Log.e(TAG, "无法在端口 " + SERVER_PORT + " 上启动服务器: " + e.getMessage(), e);
            } finally {
                Log.i(TAG, "服务器Socket监听线程结束。");
                closeServerSocket();
            }
        });
    }
    
    /**
     * 停止所有网络连接
     */
    public void stopAllNetworkConnections() {
        Log.i(TAG, "开始停止所有网络连接");
        mServiceHandler.post(() -> {
            // 首先主动断开所有客户端连接
            synchronized (mActiveClientHandlers) {
                Log.i(TAG, "主动断开 " + mActiveClientHandlers.size() + " 个活跃客户端连接");
                for (ClientSocketHandler handler : mActiveClientHandlers) {
                    try {
                        handler.forceDisconnect();
                    } catch (Exception e) {
                        Log.w(TAG, "强制断开客户端时出错: " + e.getMessage());
                    }
                }
                mActiveClientHandlers.clear();
            }
            
            // 停止服务器Socket
            closeServerSocket();
            
            // 停止客户端处理线程池
            if (mClientHandlingExecutor != null && !mClientHandlingExecutor.isShutdown()) {
                mClientHandlingExecutor.shutdownNow();
                Log.i(TAG, "客户端处理线程池已停止");
            }
            
            Log.i(TAG, "所有网络连接已停止");
        });
    }
    
    /**
     * 关闭服务器Socket
     */
    private void closeServerSocket() {
        try {
            if (mServerSocket != null && !mServerSocket.isClosed()) {
                mServerSocket.close();
                Log.i(TAG, "ServerSocket已显式关闭。");
            }
        } catch (IOException e) {
            Log.e(TAG, "关闭ServerSocket时发生错误: " + e.getMessage(), e);
        }
        mServerSocket = null;
    }
    
    /**
     * 移除客户端处理器
     */
    public void removeClientHandler(ClientSocketHandler handler) {
        mActiveClientHandlers.remove(handler);
    }
    
    /**
     * 设置Activity回调
     */
    public void setActivityCallback(ActivityCallback callback) {
        synchronized (mActivityCallbackLock) {
            mActivityCallback = callback;
        }
        Log.d(TAG, "ActivityCallback " + (callback != null ? "已设置。" : "已清除。"));
    }
    
    /**
     * 获取Activity回调
     */
    public ActivityCallback getActivityCallback() {
        synchronized (mActivityCallbackLock) {
            return mActivityCallback;
        }
    }
    
    /**
     * 获取活跃客户端处理器数量
     */
    public int getActiveClientCount() {
        return mActiveClientHandlers.size();
    }

    /**
     * 在MediaProjection权限获取后重试请求
     */
    public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientIdFromActivity) {
        Log.i(TAG, "MP权限已获取，重新处理请求: " + serviceRequestId);

        PendingVDRequest request = mConnectionManager.getPendingVDRequest(serviceRequestId);
        if (request != null) {
            // 如果Activity提供了新的内部客户端ID，则使用它
            if (assignedInternalClientIdFromActivity != -1) {
                request.setAssignedInternalClientId(assignedInternalClientIdFromActivity);
            } else if (request.getAssignedInternalClientId() == -1) {
                // 如果还没有分配ID，则生成一个新的
                request.setAssignedInternalClientId(mConnectionManager.generateNewInternalClientId());
            }

            // 获取ActivityCallback并触发UI创建
            ActivityCallback activityCallback = getActivityCallback();
            if (activityCallback != null) {
                final int finalIdToUse = request.getAssignedInternalClientId();
                final String finalServiceRequestId = serviceRequestId;
                final String finalUserIdentifier = userIdentifier;

                // 在主线程上触发UI创建
                mMainThreadHandler.post(() -> {
                    activityCallback.onRequestNewVirtualDisplayUI(
                        finalServiceRequestId, finalUserIdentifier, finalIdToUse);
                });

                Log.i(TAG, "已触发UI创建，请求ID: " + serviceRequestId + ", 内部ID: " + finalIdToUse);
            } else {
                Log.e(TAG, "MP就绪后重试请求 " + serviceRequestId + " 失败，ActivityCallback为null。");
                request.setResult(request.getAssignedInternalClientId(), "ERROR_CALLBACK_NULL_ON_MP_RETRY");
            }
        } else {
            Log.w(TAG, "MP就绪后尝试重试请求，但未找到挂起请求: " + serviceRequestId);
        }
    }
}
