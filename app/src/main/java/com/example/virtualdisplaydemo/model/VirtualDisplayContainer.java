package com.example.virtualdisplaydemo.model;

import android.hardware.display.VirtualDisplay;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.GuardedBy;
import androidx.annotation.Nullable;

/**
 * 虚拟显示容器 - 封装虚拟显示相关的所有信息和状态
 */
public class VirtualDisplayContainer {
    private static final String TAG = "VirtualDisplayContainer";
    
    private final VirtualDisplay virtualDisplay;
    private final Surface targetSurface;
    private final int displayId;
    private final int internalClientId;
    private final String initialUserIdentifier;
    private final boolean isManuallyCreated;
    private final long creationTimestamp;
    private final Object lock = new Object();
    
    @GuardedBy("lock")
    private boolean isOccupiedByNetworkClient;
    @GuardedBy("lock")
    private @Nullable String occupyingUserIdentifier;
    @GuardedBy("lock")
    private long lastOccupationChangeTime;
    @GuardedBy("lock")
    private long lastActivityTime;
    
    @Nullable
    private final String errorMessage;
    
    /**
     * 正常创建的构造函数
     */
    public VirtualDisplayContainer(VirtualDisplay virtualDisplay, Surface targetSurface,
                                   int internalClientId, String initialUserIdentifier, boolean isManuallyCreated) {
        this.virtualDisplay = virtualDisplay;
        this.targetSurface = targetSurface;
        this.internalClientId = internalClientId;
        this.initialUserIdentifier = initialUserIdentifier;
        this.isManuallyCreated = isManuallyCreated;
        this.isOccupiedByNetworkClient = false;
        this.occupyingUserIdentifier = null;
        this.creationTimestamp = System.currentTimeMillis();
        this.lastOccupationChangeTime = this.creationTimestamp;
        this.lastActivityTime = this.creationTimestamp;
        this.errorMessage = null;
        
        if (virtualDisplay != null && virtualDisplay.getDisplay() != null) {
            this.displayId = virtualDisplay.getDisplay().getDisplayId();
        } else {
            this.displayId = -1;
            Log.e(TAG, "VDContainer为ID=" + internalClientId + "创建，但其VD或Display对象无效。");
        }
        
        Log.i(TAG, "VDContainer已创建: InternalID=" + internalClientId + 
              ", 用户='" + initialUserIdentifier + "', DisplayID=" + this.displayId + 
              ", 手动=" + isManuallyCreated);
    }
    
    /**
     * 错误情况的构造函数
     */
    private VirtualDisplayContainer(int internalClientId, String initialUserIdentifier, 
                                   boolean isManuallyCreated, String error) {
        this.virtualDisplay = null;
        this.targetSurface = null;
        this.internalClientId = internalClientId;
        this.initialUserIdentifier = initialUserIdentifier;
        this.isManuallyCreated = isManuallyCreated;
        this.creationTimestamp = System.currentTimeMillis();
        this.displayId = -1;
        this.errorMessage = error;
        this.isOccupiedByNetworkClient = false;
        this.occupyingUserIdentifier = null;
        this.lastOccupationChangeTime = this.creationTimestamp;
        this.lastActivityTime = this.creationTimestamp;
        
        Log.e(TAG, "VDContainer创建失败记录: InternalID=" + internalClientId + 
              ", 用户='" + initialUserIdentifier + "', 手动=" + isManuallyCreated + 
              ", 错误: " + error);
    }
    
    /**
     * 创建错误容器的静态工厂方法
     */
    public static VirtualDisplayContainer createErrorContainer(int internalClientId, 
                                                              String initialUserIdentifier, 
                                                              boolean isManuallyCreated, 
                                                              String error) {
        return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManuallyCreated, error);
    }
    
    /**
     * 尝试占用虚拟显示
     */
    public boolean tryOccupy(String networkUserIdentifier) {
        synchronized (lock) {
            if (!isManuallyCreated) {
                Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: 非手动VD。");
                return false;
            }
            if (isOccupiedByNetworkClient) {
                Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: 已被 '" + 
                      occupyingUserIdentifier + "' 占用。");
                return false;
            }
            if (virtualDisplay == null || virtualDisplay.getDisplay() == null) {
                Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: VD对象无效。");
                return false;
            }
            
            isOccupiedByNetworkClient = true;
            occupyingUserIdentifier = networkUserIdentifier;
            lastOccupationChangeTime = System.currentTimeMillis();
            
            Log.i(TAG, "手动VD (InternalID: " + internalClientId + ", DisplayID: " + displayId + 
                  ") 被网络用户 '" + networkUserIdentifier + "' 占用。");
            return true;
        }
    }
    
    /**
     * 释放占用
     */
    public void releaseOccupation() {
        synchronized (lock) {
            if (isManuallyCreated && isOccupiedByNetworkClient) {
                Log.i(TAG, "手动VD (InternalID: " + internalClientId + ", DisplayID: " + displayId + 
                      ") 被网络用户 '" + occupyingUserIdentifier + "' 释放。变为空闲。");
                isOccupiedByNetworkClient = false;
                occupyingUserIdentifier = null;
                lastOccupationChangeTime = System.currentTimeMillis();
            }
        }
    }
    
    /**
     * 检查是否被网络客户端占用
     */
    public boolean isCurrentlyOccupiedByNetwork() {
        synchronized (lock) {
            return isOccupiedByNetworkClient;
        }
    }
    
    /**
     * 获取占用的网络用户
     */
    public @Nullable String getOccupyingNetworkUser() {
        synchronized (lock) {
            return occupyingUserIdentifier;
        }
    }
    
    // Getter methods
    public VirtualDisplay getVirtualDisplay() {
        return virtualDisplay;
    }
    
    public Surface getTargetSurface() {
        return targetSurface;
    }
    
    public int getDisplayId() {
        return displayId;
    }
    
    public int getInternalClientId() {
        return internalClientId;
    }
    
    public String getInitialUserIdentifier() {
        return initialUserIdentifier;
    }
    
    public boolean isManuallyCreated() {
        return isManuallyCreated;
    }
    
    public long getCreationTime() {
        return creationTimestamp;
    }
    
    public @Nullable String getErrorMessage() {
        return errorMessage;
    }
    
    public boolean hasError() {
        return errorMessage != null;
    }
    
    /**
     * 更新活动时间
     */
    public void updateActivityTime() {
        synchronized (lock) {
            lastActivityTime = System.currentTimeMillis();
        }
    }

    /**
     * 获取最后活动时间
     */
    public long getLastActivityTime() {
        synchronized (lock) {
            return lastActivityTime;
        }
    }

    /**
     * 获取占用状态信息
     */
    public OccupationStatus getOccupationStatus() {
        synchronized (lock) {
            return new OccupationStatus(isOccupiedByNetworkClient, occupyingUserIdentifier, lastOccupationChangeTime);
        }
    }
    
    /**
     * 占用状态信息类
     */
    public static class OccupationStatus {
        public final boolean isOccupied;
        public final @Nullable String occupyingUser;
        public final long lastChangeTime;
        
        OccupationStatus(boolean isOccupied, @Nullable String occupyingUser, long lastChangeTime) {
            this.isOccupied = isOccupied;
            this.occupyingUser = occupyingUser;
            this.lastChangeTime = lastChangeTime;
        }
    }
}
