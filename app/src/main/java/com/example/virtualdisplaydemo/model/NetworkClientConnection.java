package com.example.virtualdisplaydemo.model;

/**
 * 网络客户端连接信息
 */
public class NetworkClientConnection {
    public final String userIdentifier;
    public final long connectionTime;
    public final String connectionId;
    
    public NetworkClientConnection(String userIdentifier, String connectionId) {
        this.userIdentifier = userIdentifier;
        this.connectionId = connectionId;
        this.connectionTime = System.currentTimeMillis();
    }
    
    /**
     * 获取连接持续时间（毫秒）
     */
    public long getConnectionDuration() {
        return System.currentTimeMillis() - connectionTime;
    }
    
    /**
     * 获取连接ID的简短版本（前4位）
     */
    public String getShortConnectionId() {
        if (connectionId != null && !connectionId.isEmpty()) {
            return connectionId.length() >= 4 ? connectionId.substring(0, 4) : connectionId;
        }
        return "N/A";
    }
}
