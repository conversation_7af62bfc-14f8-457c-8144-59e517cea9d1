package com.example.virtualdisplaydemo.model;

import androidx.annotation.GuardedBy;
import androidx.annotation.Nullable;

import java.io.PrintWriter;
import java.net.Socket;
import java.util.concurrent.CountDownLatch;

/**
 * 待处理的虚拟显示请求
 */
public class PendingVDRequest {
    public final String serviceRequestId;
    public final String userIdentifier;
    public final Socket clientSocket;
    public final PrintWriter writer;
    public final CountDownLatch completionLatch = new CountDownLatch(1);
    public final boolean forceCreateNew;
    
    @GuardedBy("this")
    private String resultingDisplayId = null;
    @GuardedBy("this")
    private int assignedInternalClientId = -1;
    @GuardedBy("this")
    private boolean mediaProjectionWasDenied = false;
    
    public PendingVDRequest(String serviceRequestId, String userIdentifier, 
                           Socket clientSocket, PrintWriter writer, boolean forceCreateNew) {
        this.serviceRequestId = serviceRequestId;
        this.userIdentifier = userIdentifier;
        this.clientSocket = clientSocket;
        this.writer = writer;
        this.forceCreateNew = forceCreateNew;
    }
    
    public synchronized void setResult(int internalClientId, @Nullable String displayId) {
        this.assignedInternalClientId = internalClientId;
        this.resultingDisplayId = displayId;
        this.completionLatch.countDown();
    }
    
    public synchronized void setMediaProjectionDenied() {
        this.mediaProjectionWasDenied = true;
        this.completionLatch.countDown();
    }
    
    public synchronized boolean wasMediaProjectionDenied() {
        return this.mediaProjectionWasDenied;
    }
    
    public synchronized String getResultingDisplayId() {
        return this.resultingDisplayId;
    }
    
    public synchronized int getAssignedInternalClientId() {
        return this.assignedInternalClientId;
    }

    public synchronized void setAssignedInternalClientId(int internalClientId) {
        this.assignedInternalClientId = internalClientId;
    }
}
