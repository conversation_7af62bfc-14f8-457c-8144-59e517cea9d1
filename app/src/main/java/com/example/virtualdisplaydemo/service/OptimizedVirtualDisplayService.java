package com.example.virtualdisplaydemo.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.media.projection.MediaProjection;
import android.os.Binder;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.example.virtualdisplaydemo.manager.VirtualDisplayManager;
import com.example.virtualdisplaydemo.manager.NetworkServerManager;
import com.example.virtualdisplaydemo.manager.OptimizedClientManager;
import com.example.virtualdisplaydemo.manager.AppLaunchManager;
import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;
import com.example.virtualdisplaydemo.callback.ActivityCallback;
import com.example.virtualdisplaydemo.config.OptimizationConfig;

/**
 * 优化版虚拟显示管理服务
 * 集成了内存优化、连接池化和性能监控功能
 */
public class OptimizedVirtualDisplayService extends Service {
    private static final String TAG = "OptimizedVDService";
    private static final String NOTIFICATION_CHANNEL_ID = "OptimizedVDServiceChannel";
    private static final int SERVICE_NOTIFICATION_ID = 106;

    // 管理器组件
    private VirtualDisplayManager mVirtualDisplayManager;
    private NetworkServerManager mNetworkServerManager;
    private OptimizedClientManager mOptimizedClientManager;
    private AppLaunchManager mAppLaunchManager;

    // 线程处理
    private HandlerThread mServiceHandlerThread;
    private Handler mServiceHandler;
    private Handler mMainThreadHandler;

    // 性能监控
    private PerformanceMonitor mPerformanceMonitor;

    // 服务绑定
    private final IBinder mBinder = new LocalBinder();

    /**
     * 本地绑定器 - 提供服务访问接口
     */
    public class LocalBinder extends Binder {
        public OptimizedVirtualDisplayService getService() {
            return OptimizedVirtualDisplayService.this;
        }

        public void setActivityCallback(@Nullable ActivityCallback callback) {
            if (mNetworkServerManager != null) {
                mNetworkServerManager.setActivityCallback(callback);
            }
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "优化版虚拟显示服务启动中...");

        // 初始化线程处理
        initializeThreads();

        // 初始化管理器组件
        initializeManagers();

        // 启动性能监控
        if (OptimizationConfig.ENABLE_PERFORMANCE_MONITORING) {
            mPerformanceMonitor = new PerformanceMonitor();
            mPerformanceMonitor.start();
        }

        // 创建通知并启动前台服务
        createNotificationChannel();
        startForeground(SERVICE_NOTIFICATION_ID, createServiceNotification());

        // 启动网络服务器
        mNetworkServerManager.startServerListener();

        Log.i(TAG, "优化版虚拟显示服务已启动 - " + OptimizationConfig.getConfigSummary());
    }

    /**
     * 初始化线程
     */
    private void initializeThreads() {
        mServiceHandlerThread = new HandlerThread(TAG + "_OptimizedBgThread");
        mServiceHandlerThread.start();
        mServiceHandler = new Handler(mServiceHandlerThread.getLooper());
        mMainThreadHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 初始化所有管理器组件
     */
    private void initializeManagers() {
        // 初始化虚拟显示管理器（已优化版本）
        mVirtualDisplayManager = VirtualDisplayManager.getInstance(this);

        // 初始化优化的客户端管理器
        mOptimizedClientManager = new OptimizedClientManager();

        // 初始化应用启动管理器
        mAppLaunchManager = new AppLaunchManager(this, mServiceHandler, mMainThreadHandler);

        // 初始化网络服务器管理器（需要适配新的客户端管理器）
        // 注意：这里需要修改NetworkServerManager以使用OptimizedClientManager
        // mNetworkServerManager = new NetworkServerManager(mServiceHandler,
        //                                                 mMainThreadHandler,
        //                                                 mVirtualDisplayManager,
        //                                                 mOptimizedClientManager,
        //                                                 mAppLaunchManager);

        Log.d(TAG, "所有优化管理器组件已初始化完成");
    }

    /**
     * 创建虚拟显示 - 优化版本
     */
    public @Nullable VirtualDisplayContainer createVirtualDisplayForClientWithProjection(
            int internalClientId, @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, @NonNull String initialUserIdentifier, boolean isManual) {

        // 检查是否可以创建新显示器
        if (!mVirtualDisplayManager.canCreateNewDisplay()) {
            Log.w(TAG, "无法创建新显示器：资源限制或内存不足");
            return null;
        }

        return mVirtualDisplayManager.createVirtualDisplay(internalClientId, targetSurface,
                                                          mediaProjection, initialUserIdentifier, isManual);
    }

    /**
     * 释放虚拟显示 - 优化版本
     */
    public void releaseVirtualDisplay(int internalClientId, boolean calledFromSurfaceDestroyed) {
        mServiceHandler.post(() -> {
            mVirtualDisplayManager.releaseVirtualDisplay(internalClientId);
        });
    }

    /**
     * 获取服务状态信息
     */
    public ServiceStatus getServiceStatus() {
        OptimizedClientManager.ConnectionStats connectionStats = mOptimizedClientManager.getConnectionStats();
        int activeDisplays = mVirtualDisplayManager.getActiveDisplayCount();
        int memoryUsage = OptimizationConfig.getCurrentMemoryUsagePercent();

        return new ServiceStatus(
            activeDisplays,
            connectionStats.activeConnections,
            connectionStats.pendingRequests,
            memoryUsage,
            OptimizationConfig.getOptimalMaxDisplays()
        );
    }

    /**
     * 停止所有网络连接
     */
    public void stopAllNetworkConnections() {
        if (mNetworkServerManager != null) {
            mNetworkServerManager.stopAllNetworkConnections();
        }
        if (mOptimizedClientManager != null) {
            mOptimizedClientManager.clearAllConnections();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "优化版服务被绑定");
        return mBinder;
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "优化版虚拟显示服务正在销毁...");

        // 停止性能监控
        if (mPerformanceMonitor != null) {
            mPerformanceMonitor.stop();
        }

        // 停止所有网络连接
        stopAllNetworkConnections();

        // 关闭优化客户端管理器
        if (mOptimizedClientManager != null) {
            mOptimizedClientManager.shutdown();
        }

        // 停止服务线程
        if (mServiceHandlerThread != null) {
            mServiceHandlerThread.quitSafely();
            try {
                mServiceHandlerThread.join(3000);
            } catch (InterruptedException e) {
                Log.w(TAG, "等待服务线程结束被中断");
            }
        }

        super.onDestroy();
        Log.i(TAG, "优化版虚拟显示服务已销毁");
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "优化版虚拟显示管理服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("优化版虚拟显示和网络连接管理服务");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    /**
     * 创建服务通知
     */
    private Notification createServiceNotification() {
        Intent notificationIntent = new Intent(this, getClass());
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        ServiceStatus status = getServiceStatus();
        String contentText = String.format("显示器:%d/%d 连接:%d 内存:%d%%",
            status.activeDisplays, status.maxDisplays, status.activeConnections, status.memoryUsage);

        return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("优化版虚拟显示服务")
            .setContentText(contentText)
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build();
    }

    /**
     * 性能监控器
     */
    private class PerformanceMonitor {
        private Thread mMonitorThread;
        private volatile boolean mIsRunning = false;

        void start() {
            if (mIsRunning) return;

            mIsRunning = true;
            mMonitorThread = new Thread(this::monitorLoop, "PerformanceMonitor");
            mMonitorThread.setDaemon(true);
            mMonitorThread.start();
            Log.i(TAG, "性能监控器已启动");
        }

        void stop() {
            mIsRunning = false;
            if (mMonitorThread != null) {
                mMonitorThread.interrupt();
            }
        }

        private void monitorLoop() {
            try {
                while (mIsRunning && !Thread.currentThread().isInterrupted()) {
                    Thread.sleep(OptimizationConfig.PERFORMANCE_REPORT_INTERVAL_MS);

                    ServiceStatus status = getServiceStatus();
                    Log.i(TAG, "性能报告: " + status.toString());

                    // 更新通知内容
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        NotificationManager notificationManager = getSystemService(NotificationManager.class);
                        if (notificationManager != null) {
                            notificationManager.notify(SERVICE_NOTIFICATION_ID, createServiceNotification());
                        }
                    }
                }
            } catch (InterruptedException e) {
                Log.i(TAG, "性能监控线程被中断");
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 服务状态信息
     */
    public static class ServiceStatus {
        public final int activeDisplays;
        public final int activeConnections;
        public final int pendingRequests;
        public final int memoryUsage;
        public final int maxDisplays;

        ServiceStatus(int activeDisplays, int activeConnections, int pendingRequests,
                     int memoryUsage, int maxDisplays) {
            this.activeDisplays = activeDisplays;
            this.activeConnections = activeConnections;
            this.pendingRequests = pendingRequests;
            this.memoryUsage = memoryUsage;
            this.maxDisplays = maxDisplays;
        }

        @Override
        public String toString() {
            return "ServiceStatus{" +
                   "displays=" + activeDisplays + "/" + maxDisplays +
                   ", connections=" + activeConnections +
                   ", pending=" + pendingRequests +
                   ", memory=" + memoryUsage + "%" +
                   '}';
        }
    }
}
