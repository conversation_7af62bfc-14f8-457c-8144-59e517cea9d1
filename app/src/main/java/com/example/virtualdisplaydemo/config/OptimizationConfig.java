package com.example.virtualdisplaydemo.config;

/**
 * 优化配置类 - 集中管理所有性能和资源优化相关的配置参数
 */
public class OptimizationConfig {
    
    // ========== 虚拟显示器配置 ==========
    
    /**
     * 最大并发虚拟显示器数量
     * 建议值：3-5个，根据设备性能调整
     */
    public static final int MAX_CONCURRENT_DISPLAYS = 3;
    
    /**
     * 虚拟显示器空闲超时时间（毫秒）
     * 超过此时间未使用的显示器将被自动清理
     */
    public static final long DISPLAY_IDLE_TIMEOUT_MS = 300000; // 5分钟
    
    /**
     * 虚拟显示器默认宽度
     * 较小的分辨率可以减少内存使用
     */
    public static final int DEFAULT_DISPLAY_WIDTH = 1280;
    
    /**
     * 虚拟显示器默认高度
     */
    public static final int DEFAULT_DISPLAY_HEIGHT = 720;
    
    /**
     * 是否启用显示器池化
     * 启用后可以复用空闲的显示器，减少创建/销毁开销
     */
    public static final boolean ENABLE_DISPLAY_POOLING = true;
    
    // ========== 客户端连接配置 ==========
    
    /**
     * 最大并发客户端连接数
     */
    public static final int MAX_CONCURRENT_CONNECTIONS = 5;
    
    /**
     * 客户端连接超时时间（毫秒）
     */
    public static final long CONNECTION_TIMEOUT_MS = 600000; // 10分钟
    
    /**
     * 心跳间隔时间（毫秒）
     */
    public static final long HEARTBEAT_INTERVAL_MS = 10000; // 10秒
    
    /**
     * 是否启用连接池化
     */
    public static final boolean ENABLE_CONNECTION_POOLING = true;
    
    // ========== 内存管理配置 ==========
    
    /**
     * 内存监控检查间隔（毫秒）
     */
    public static final long MEMORY_CHECK_INTERVAL_MS = 30000; // 30秒
    
    /**
     * 内存使用率警告阈值（百分比）
     * 超过此阈值将开始清理空闲资源
     */
    public static final int MEMORY_WARNING_THRESHOLD = 80;
    
    /**
     * 内存使用率危险阈值（百分比）
     * 超过此阈值将拒绝新的连接请求
     */
    public static final int MEMORY_DANGER_THRESHOLD = 90;
    
    /**
     * 是否启用自动垃圾回收
     */
    public static final boolean ENABLE_AUTO_GC = true;
    
    // ========== 清理任务配置 ==========
    
    /**
     * 资源清理任务执行间隔（毫秒）
     */
    public static final long CLEANUP_INTERVAL_MS = 60000; // 1分钟
    
    /**
     * 是否启用定期清理
     */
    public static final boolean ENABLE_PERIODIC_CLEANUP = true;
    
    /**
     * 清理任务超时时间（毫秒）
     */
    public static final long CLEANUP_TIMEOUT_MS = 5000; // 5秒
    
    // ========== 性能优化配置 ==========
    
    /**
     * 是否启用快速模式
     * 快速模式下会优先复用现有资源而不是创建新资源
     */
    public static final boolean ENABLE_FAST_MODE = true;
    
    /**
     * 是否启用预创建显示器
     * 预创建一定数量的显示器以减少响应时间
     */
    public static final boolean ENABLE_DISPLAY_PREALLOCATION = false;
    
    /**
     * 预创建显示器数量
     */
    public static final int PREALLOCATION_COUNT = 1;
    
    /**
     * 是否启用资源预热
     * 在服务启动时预先初始化一些资源
     */
    public static final boolean ENABLE_RESOURCE_WARMUP = true;
    
    // ========== 调试和监控配置 ==========
    
    /**
     * 是否启用详细日志
     */
    public static final boolean ENABLE_VERBOSE_LOGGING = true;
    
    /**
     * 是否启用性能监控
     */
    public static final boolean ENABLE_PERFORMANCE_MONITORING = true;
    
    /**
     * 性能统计报告间隔（毫秒）
     */
    public static final long PERFORMANCE_REPORT_INTERVAL_MS = 300000; // 5分钟
    
    /**
     * 是否启用内存使用统计
     */
    public static final boolean ENABLE_MEMORY_STATS = true;
    
    // ========== 动态配置方法 ==========
    
    /**
     * 根据设备性能动态调整最大并发显示器数量
     */
    public static int getOptimalMaxDisplays() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        
        // 根据可用内存动态调整
        if (maxMemory > 512 * 1024 * 1024) { // 512MB以上
            return Math.min(MAX_CONCURRENT_DISPLAYS, 5);
        } else if (maxMemory > 256 * 1024 * 1024) { // 256MB-512MB
            return Math.min(MAX_CONCURRENT_DISPLAYS, 3);
        } else { // 256MB以下
            return Math.min(MAX_CONCURRENT_DISPLAYS, 2);
        }
    }
    
    /**
     * 根据当前内存使用情况动态调整清理策略
     */
    public static boolean shouldAggressiveCleanup() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        int memoryUsagePercent = (int) (usedMemory * 100 / maxMemory);
        return memoryUsagePercent > MEMORY_WARNING_THRESHOLD;
    }
    
    /**
     * 检查是否应该拒绝新连接
     */
    public static boolean shouldRejectNewConnections() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        int memoryUsagePercent = (int) (usedMemory * 100 / maxMemory);
        return memoryUsagePercent > MEMORY_DANGER_THRESHOLD;
    }
    
    /**
     * 获取当前内存使用率
     */
    public static int getCurrentMemoryUsagePercent() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        return (int) (usedMemory * 100 / maxMemory);
    }
    
    /**
     * 获取推荐的显示器分辨率
     * 根据内存使用情况动态调整分辨率以优化性能
     */
    public static DisplayResolution getRecommendedResolution() {
        if (shouldAggressiveCleanup()) {
            // 内存紧张时使用较低分辨率
            return new DisplayResolution(1024, 576); // 16:9 低分辨率
        } else {
            // 正常情况使用标准分辨率
            return new DisplayResolution(DEFAULT_DISPLAY_WIDTH, DEFAULT_DISPLAY_HEIGHT);
        }
    }
    
    /**
     * 显示器分辨率配置类
     */
    public static class DisplayResolution {
        public final int width;
        public final int height;
        
        public DisplayResolution(int width, int height) {
            this.width = width;
            this.height = height;
        }
        
        @Override
        public String toString() {
            return width + "x" + height;
        }
    }
    
    /**
     * 获取配置摘要信息
     */
    public static String getConfigSummary() {
        return "OptimizationConfig{" +
               "maxDisplays=" + MAX_CONCURRENT_DISPLAYS +
               ", maxConnections=" + MAX_CONCURRENT_CONNECTIONS +
               ", displayPooling=" + ENABLE_DISPLAY_POOLING +
               ", connectionPooling=" + ENABLE_CONNECTION_POOLING +
               ", memoryThreshold=" + MEMORY_WARNING_THRESHOLD + "%" +
               ", currentMemoryUsage=" + getCurrentMemoryUsagePercent() + "%" +
               '}';
    }
}
