package com.example.virtualdisplaydemo.config;

import android.util.Log;

/**
 * 监控配置类 - 提供应用监控的全局控制
 * 
 * 这个类允许用户完全控制应用监控行为，
 * 从根本上解决监控导致的闪退问题。
 */
public class MonitoringConfig {
    private static final String TAG = "MonitoringConfig";
    
    // 全局监控开关
    private static volatile boolean sAppMonitoringEnabled = false; // 默认关闭！
    private static volatile boolean sHeartbeatMonitoringEnabled = true; // 心跳监控保持开启
    
    // 监控策略
    private static volatile MonitoringStrategy sMonitoringStrategy = MonitoringStrategy.DISABLED;
    
    /**
     * 监控策略枚举
     */
    public enum MonitoringStrategy {
        DISABLED,           // 完全禁用应用监控
        PASSIVE,           // 被动监控：只记录，不断开连接
        ACTIVE,            // 主动监控：检测到应用退出时断开连接
        AGGRESSIVE         // 激进监控：严格检测应用状态
    }
    
    /**
     * 启用应用监控
     */
    public static void enableAppMonitoring() {
        sAppMonitoringEnabled = true;
        if (sMonitoringStrategy == MonitoringStrategy.DISABLED) {
            sMonitoringStrategy = MonitoringStrategy.PASSIVE;
        }
        Log.i(TAG, "应用监控已启用，策略: " + sMonitoringStrategy);
    }
    
    /**
     * 禁用应用监控
     */
    public static void disableAppMonitoring() {
        sAppMonitoringEnabled = false;
        sMonitoringStrategy = MonitoringStrategy.DISABLED;
        Log.i(TAG, "应用监控已禁用");
    }
    
    /**
     * 检查应用监控是否启用
     */
    public static boolean isAppMonitoringEnabled() {
        return sAppMonitoringEnabled;
    }
    
    /**
     * 设置监控策略
     */
    public static void setMonitoringStrategy(MonitoringStrategy strategy) {
        sMonitoringStrategy = strategy;
        sAppMonitoringEnabled = (strategy != MonitoringStrategy.DISABLED);
        Log.i(TAG, "监控策略已设置为: " + strategy);
    }
    
    /**
     * 获取当前监控策略
     */
    public static MonitoringStrategy getMonitoringStrategy() {
        return sMonitoringStrategy;
    }
    
    /**
     * 检查是否应该启动应用监控
     */
    public static boolean shouldStartAppMonitoring(String packageName) {
        if (!sAppMonitoringEnabled) {
            Log.d(TAG, "应用监控已禁用，跳过监控: " + packageName);
            return false;
        }
        
        if (sMonitoringStrategy == MonitoringStrategy.DISABLED) {
            Log.d(TAG, "监控策略为禁用，跳过监控: " + packageName);
            return false;
        }
        
        if (packageName == null || packageName.trim().isEmpty()) {
            Log.d(TAG, "包名为空，跳过监控");
            return false;
        }
        
        Log.d(TAG, "允许启动应用监控: " + packageName + ", 策略: " + sMonitoringStrategy);
        return true;
    }
    
    /**
     * 检查应用退出时是否应该断开连接
     */
    public static boolean shouldDisconnectOnAppExit() {
        return sMonitoringStrategy == MonitoringStrategy.ACTIVE || 
               sMonitoringStrategy == MonitoringStrategy.AGGRESSIVE;
    }
    
    /**
     * 检查是否应该使用严格的应用检测
     */
    public static boolean shouldUseStrictAppDetection() {
        return sMonitoringStrategy == MonitoringStrategy.AGGRESSIVE;
    }
    
    /**
     * 获取应用检测间隔（毫秒）
     */
    public static long getAppCheckInterval() {
        switch (sMonitoringStrategy) {
            case PASSIVE:
                return 30000; // 30秒
            case ACTIVE:
                return 15000; // 15秒
            case AGGRESSIVE:
                return 5000;  // 5秒
            default:
                return 60000; // 60秒（不应该到达这里）
        }
    }
    
    /**
     * 获取最大连续失败次数
     */
    public static int getMaxConsecutiveFailures() {
        switch (sMonitoringStrategy) {
            case PASSIVE:
                return 5;  // 宽松
            case ACTIVE:
                return 3;  // 中等
            case AGGRESSIVE:
                return 2;  // 严格
            default:
                return 10; // 非常宽松
        }
    }
    
    /**
     * 心跳监控控制
     */
    public static boolean isHeartbeatMonitoringEnabled() {
        return sHeartbeatMonitoringEnabled;
    }
    
    public static void enableHeartbeatMonitoring() {
        sHeartbeatMonitoringEnabled = true;
        Log.i(TAG, "心跳监控已启用");
    }
    
    public static void disableHeartbeatMonitoring() {
        sHeartbeatMonitoringEnabled = false;
        Log.i(TAG, "心跳监控已禁用");
    }
    
    /**
     * 获取配置摘要
     */
    public static String getConfigSummary() {
        return "MonitoringConfig{" +
               "appMonitoring=" + sAppMonitoringEnabled +
               ", strategy=" + sMonitoringStrategy +
               ", heartbeat=" + sHeartbeatMonitoringEnabled +
               ", checkInterval=" + getAppCheckInterval() + "ms" +
               ", maxFailures=" + getMaxConsecutiveFailures() +
               '}';
    }
    
    /**
     * 重置为默认配置
     */
    public static void resetToDefault() {
        sAppMonitoringEnabled = false; // 默认关闭应用监控
        sHeartbeatMonitoringEnabled = true;
        sMonitoringStrategy = MonitoringStrategy.DISABLED;
        Log.i(TAG, "监控配置已重置为默认值");
    }
    
    /**
     * 设置为推荐的安全配置
     */
    public static void setRecommendedSafeConfig() {
        sAppMonitoringEnabled = true;
        sHeartbeatMonitoringEnabled = true;
        sMonitoringStrategy = MonitoringStrategy.PASSIVE; // 被动监控最安全
        Log.i(TAG, "已设置为推荐的安全配置: " + getConfigSummary());
    }
    
    /**
     * 设置为调试配置
     */
    public static void setDebugConfig() {
        sAppMonitoringEnabled = true;
        sHeartbeatMonitoringEnabled = true;
        sMonitoringStrategy = MonitoringStrategy.ACTIVE;
        Log.i(TAG, "已设置为调试配置: " + getConfigSummary());
    }
}
