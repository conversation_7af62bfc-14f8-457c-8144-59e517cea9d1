package com.example.virtualdisplaydemo.callback;

import androidx.annotation.Nullable;

/**
 * Activity回调接口 - 定义服务与Activity之间的通信契约
 */
public interface ActivityCallback {
    
    /**
     * 请求创建新的虚拟显示UI
     */
    void onRequestNewVirtualDisplayUI(String serviceRequestId, String userIdentifier, int assignedInternalClientId);
    
    /**
     * 检查MediaProjection是否准备就绪
     */
    boolean isMediaProjectionReady();
    
    /**
     * 请求用户授予MediaProjection权限
     */
    void requestMediaProjectionFromUser(String serviceRequestId);
    
    /**
     * 更新手动屏幕占用状态
     */
    void updateManualScreenOccupationStatus(int internalClientId, @Nullable String occupyingUserIdentifier, 
                                           boolean isOccupied, int displayId);
    
    /**
     * 在MediaProjection权限获取后重试请求
     */
    void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientId);
    
    /**
     * 通知MediaProjection请求繁忙
     */
    void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId);
}
