package com.example.virtualdisplaydemo.handler;

import android.util.Log;

import androidx.annotation.Nullable;

import java.io.PrintWriter;

/**
 * 客户端请求解析器 - 负责解析客户端发送的请求消息
 */
public class ClientRequestParser {
    private static final String TAG = "ClientRequestParser";
    
    /**
     * 解析客户端请求消息
     */
    public static @Nullable ClientSocketHandler.ClientRequest parse(String clientMessage, 
                                                                   String connectionId, 
                                                                   PrintWriter writer) {
        if (clientMessage == null) {
            return null;
        }
        
        String userIdentifier;
        boolean forceCreateNew = false;
        String targetAppPackageName = null;
        String targetAppActivityName = null;
        
        if (clientMessage.startsWith("REQUEST_VD_SMART:")) {
            return parseSmartRequest(clientMessage, connectionId);
        } else if (clientMessage.startsWith("REQUEST_VD:")) {
            return parseLegacyRequest(clientMessage, connectionId);
        } else {
            Log.w(TAG, "来自ConnID: " + connectionId + " 的无效请求: " + clientMessage);
            writer.println("ERROR:NA:无效请求格式。");
            return null;
        }
    }
    
    /**
     * 解析智能请求格式：REQUEST_VD_SMART:user:force_new:package:activity
     */
    private static ClientSocketHandler.ClientRequest parseSmartRequest(String clientMessage, String connectionId) {
        String content = clientMessage.substring("REQUEST_VD_SMART:".length());
        String[] parts = content.split(":", 4);
        
        String userIdentifier = (parts.length > 0 && !parts[0].trim().isEmpty()) ? 
                               parts[0].trim() : "SmartUser_" + connectionId;
        
        boolean forceCreateNew = false;
        if (parts.length > 1) {
            forceCreateNew = "force_new".equalsIgnoreCase(parts[1].trim());
        }
        
        String targetAppPackageName = null;
        if (parts.length > 2 && !parts[2].trim().isEmpty()) {
            targetAppPackageName = parts[2].trim();
        }
        
        String targetAppActivityName = null;
        if (parts.length > 3 && !parts[3].trim().isEmpty()) {
            targetAppActivityName = parts[3].trim();
        }
        
        Log.i(TAG, "ConnID: " + connectionId + " - 解析结果: User=" + userIdentifier +
                ", ForceNew=" + forceCreateNew + ", Pkg=" + targetAppPackageName + 
                ", Activity=" + targetAppActivityName);
        
        return new ClientSocketHandler.ClientRequest(userIdentifier, forceCreateNew, 
                                                    targetAppPackageName, targetAppActivityName);
    }
    
    /**
     * 解析传统请求格式：REQUEST_VD:user
     */
    private static ClientSocketHandler.ClientRequest parseLegacyRequest(String clientMessage, String connectionId) {
        String userIdentifier = clientMessage.substring("REQUEST_VD:".length()).trim();
        if (userIdentifier.isEmpty()) {
            userIdentifier = "LegacyUser_" + connectionId;
        }
        
        Log.i(TAG, "ConnID: " + connectionId + " - 传统请求解析结果: User=" + userIdentifier);
        
        return new ClientSocketHandler.ClientRequest(userIdentifier, false, null, null);
    }
}
