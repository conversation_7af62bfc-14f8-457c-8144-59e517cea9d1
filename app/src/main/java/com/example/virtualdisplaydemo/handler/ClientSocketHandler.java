package com.example.virtualdisplaydemo.handler;

import android.util.Log;

import com.example.virtualdisplaydemo.manager.VirtualDisplayManager;
import com.example.virtualdisplaydemo.manager.ClientConnectionManager;
import com.example.virtualdisplaydemo.manager.AppLaunchManager;
import com.example.virtualdisplaydemo.manager.NetworkServerManager;
import com.example.virtualdisplaydemo.model.PendingVDRequest;
import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;
import com.example.virtualdisplaydemo.processor.VirtualDisplayRequestProcessor;
import com.example.virtualdisplaydemo.monitor.HeartbeatMonitor;
import com.example.virtualdisplaydemo.monitor.AppMonitor;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.net.SocketException;
import java.util.UUID;

/**
 * 客户端Socket处理器 - 处理单个客户端连接的所有逻辑
 * 使用组合模式将复杂的处理逻辑分解到不同的组件中
 */
public class ClientSocketHandler implements Runnable {
    private static final String TAG = "ClientSocketHandler";
    
    private final Socket mClientSocket;
    private final String mConnectionId;
    private final String mClientInfo;
    private BufferedReader mReader;
    private PrintWriter mWriter;
    private volatile boolean mIsClientAlive = true;
    
    // 依赖的管理器
    private final VirtualDisplayManager mVirtualDisplayManager;
    private final ClientConnectionManager mConnectionManager;
    private final AppLaunchManager mAppLaunchManager;
    private final NetworkServerManager mNetworkServerManager;
    
    // 处理器和监控器
    private VirtualDisplayRequestProcessor mRequestProcessor;
    private HeartbeatMonitor mHeartbeatMonitor;
    private AppMonitor mAppMonitor;
    
    // 当前处理的请求和VD
    private PendingVDRequest mPendingVDRequest;
    private VirtualDisplayContainer mReusedManualVD;
    private String mTargetAppPackageName;
    
    public ClientSocketHandler(Socket socket, 
                              VirtualDisplayManager virtualDisplayManager,
                              ClientConnectionManager connectionManager,
                              AppLaunchManager appLaunchManager,
                              NetworkServerManager networkServerManager) {
        this.mClientSocket = socket;
        this.mConnectionId = UUID.randomUUID().toString().substring(0, 8);
        this.mClientInfo = socket.getRemoteSocketAddress().toString();
        this.mVirtualDisplayManager = virtualDisplayManager;
        this.mConnectionManager = connectionManager;
        this.mAppLaunchManager = appLaunchManager;
        this.mNetworkServerManager = networkServerManager;
    }
    
    @Override
    public void run() {
        String serviceReqIdForVD = UUID.randomUUID().toString();
        Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 启动。");
        
        try {
            // 初始化IO流
            initializeStreams();
            
            // 发送欢迎消息
            sendWelcomeMessage();
            
            // 解析客户端请求
            ClientRequest request = parseClientRequest();
            if (request == null) {
                return; // 解析失败，退出
            }
            
            // 创建请求处理器
            mRequestProcessor = new VirtualDisplayRequestProcessor(
                mVirtualDisplayManager, mConnectionManager, mNetworkServerManager);
            
            // 处理虚拟显示请求
            mPendingVDRequest = new PendingVDRequest(serviceReqIdForVD, request.userIdentifier, 
                                                    mClientSocket, mWriter, request.forceCreateNew);
            mConnectionManager.addPendingVDRequest(serviceReqIdForVD, mPendingVDRequest);
            
            boolean success = mRequestProcessor.handleVirtualDisplayRequest(mPendingVDRequest, mConnectionId);
            
            if (success && isVDCreatedSuccessfully()) {
                Log.d(TAG, "ConnID: " + mConnectionId + " - Display已分配给 " + mClientInfo + "，启动心跳并保持连接...");
                
                // 启动心跳监控
                startHeartbeatMonitor();
                
                // 处理应用启动
                handleAppLaunch(request, serviceReqIdForVD);
                
                // 保持连接
                maintainConnection();
            } else {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 未能成功分配Display，或出错/超时/连接提前关闭。");
            }
            
        } catch (SocketException se) {
            handleSocketException(se);
        } catch (IOException ioe) {
            Log.e(TAG, "ClientSocketHandler IOException (客户端 " + mClientInfo + 
                  ", ConnID: " + mConnectionId + "): " + ioe.getMessage(), ioe);
        } catch (InterruptedException ie) {
            Log.w(TAG, "ClientSocketHandler (客户端 " + mClientInfo + 
                  ", ConnID: " + mConnectionId + ") 被中断: " + ie.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            Log.e(TAG, "ClientSocketHandler (客户端 " + mClientInfo + 
                  ", ConnID: " + mConnectionId + ") 发生未预期异常: " + e.getMessage(), e);
        } finally {
            cleanup();
        }
    }
    
    /**
     * 初始化IO流
     */
    private void initializeStreams() throws IOException {
        mReader = new BufferedReader(new InputStreamReader(mClientSocket.getInputStream()));
        mWriter = new PrintWriter(mClientSocket.getOutputStream(), true);
    }
    
    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage() {
        mWriter.println("DSMS_CONNECTED:OK:Welcome_Smart_DSMS_v1.0_CN_HB_AppLaunch");
    }
    
    /**
     * 解析客户端请求
     */
    private ClientRequest parseClientRequest() throws IOException {
        String clientMessage = mReader.readLine();
        if (clientMessage == null) {
            Log.w(TAG, "客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 在发送数据前已断开。");
            return null;
        }
        
        Log.i(TAG, "客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 发送: " + clientMessage);
        
        return ClientRequestParser.parse(clientMessage, mConnectionId, mWriter);
    }
    
    /**
     * 检查VD是否创建成功
     */
    private boolean isVDCreatedSuccessfully() {
        return mPendingVDRequest != null && 
               mPendingVDRequest.getResultingDisplayId() != null &&
               !mPendingVDRequest.getResultingDisplayId().startsWith("ERROR_") &&
               mClientSocket != null && !mClientSocket.isClosed() && mReader != null;
    }
    
    /**
     * 启动心跳监控
     */
    private void startHeartbeatMonitor() {
        mHeartbeatMonitor = new HeartbeatMonitor(mConnectionId, mWriter, () -> mIsClientAlive);
        mHeartbeatMonitor.start();
    }
    
    /**
     * 处理应用启动
     */
    private void handleAppLaunch(ClientRequest request, String serviceReqIdForVD) {
        if (request.targetAppPackageName != null && request.targetAppActivityName != null) {
            mTargetAppPackageName = request.targetAppPackageName;
            String displayId = mPendingVDRequest.getResultingDisplayId();
            
            Log.i(TAG, "ConnID: " + mConnectionId + " - 准备启动应用: " + 
                  request.targetAppPackageName + "/" + request.targetAppActivityName + 
                  " 到显示器 " + displayId);
            
            mAppLaunchManager.launchAppOnDisplay(request.targetAppPackageName, 
                                               request.targetAppActivityName, 
                                               displayId, serviceReqIdForVD, mConnectionId, mWriter);
            
            // 启动应用监控
            startAppMonitor();
        } else {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 应用启动参数不完整，跳过应用启动");
        }
    }
    
    /**
     * 启动应用监控
     */
    private void startAppMonitor() {
        if (mTargetAppPackageName != null) {
            mAppMonitor = new AppMonitor(mConnectionId, mTargetAppPackageName, 
                                       mAppLaunchManager, () -> mIsClientAlive, 
                                       this::disconnectClient);
            mAppMonitor.start();
        }
    }
    
    /**
     * 断开客户端连接
     */
    private void disconnectClient() {
        mIsClientAlive = false;
        try {
            if (mClientSocket != null && !mClientSocket.isClosed()) {
                mClientSocket.close();
            }
        } catch (IOException e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 关闭Socket时出错: " + e.getMessage());
        }
    }
    
    /**
     * 保持连接并处理消息
     */
    private void maintainConnection() {
        try {
            String line;
            while ((line = mReader.readLine()) != null && mIsClientAlive) {
                handleIncomingMessage(line);
            }
            Log.i(TAG, "ConnID: " + mConnectionId + " - 客户端 " + mClientInfo + " 已断开 (readLine返回null)。");
        } catch (Exception e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 连接维护异常: " + e.getMessage());
        } finally {
            mIsClientAlive = false;
            if (mHeartbeatMonitor != null) {
                mHeartbeatMonitor.stop();
            }
        }
    }
    
    /**
     * 处理收到的消息
     */
    private void handleIncomingMessage(String message) {
        if ("PY_HEARTBEAT_PING".equals(message.trim())) {
            // 响应客户端心跳
            if (mWriter != null && !mWriter.checkError()) {
                mWriter.println("DSMS_HEARTBEAT_PONG");
                Log.d(TAG, "ConnID: " + mConnectionId + " - 响应客户端PING with PONG");
            }
        } else if ("DSMS_HEARTBEAT_PONG".equals(message.trim())) {
            // 客户端响应我们的心跳
            Log.d(TAG, "ConnID: " + mConnectionId + " - 收到客户端PONG响应");
        } else {
            Log.d(TAG, "ConnID: " + mConnectionId + " - 收到其他消息: " + message);
        }
    }
    
    /**
     * 处理Socket异常
     */
    private void handleSocketException(SocketException se) {
        if (se.getMessage() != null && 
            (se.getMessage().toLowerCase().contains("socket closed") || 
             se.getMessage().toLowerCase().contains("connection reset") || 
             se.getMessage().toLowerCase().contains("broken pipe"))) {
            Log.i(TAG, "ConnID: " + mConnectionId + " - SocketException表明客户端已关闭连接: " + se.getMessage());
        } else {
            Log.w(TAG, "ClientSocketHandler SocketException (客户端 " + mClientInfo + 
                  ", ConnID: " + mConnectionId + "): " + se.getMessage());
        }
    }
    
    /**
     * 强制断开连接
     */
    public void forceDisconnect() {
        Log.i(TAG, "ConnID: " + mConnectionId + " - 强制断开客户端连接");
        mIsClientAlive = false;
        
        // 发送关闭通知
        if (mWriter != null && !mWriter.checkError()) {
            try {
                mWriter.println("SERVER_SHUTDOWN:Service_Stopping");
                mWriter.flush();
                Log.d(TAG, "ConnID: " + mConnectionId + " - 已发送强制关闭通知");
            } catch (Exception e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 发送强制关闭通知失败: " + e.getMessage());
            }
        }
        
        // 关闭Socket
        try {
            if (mClientSocket != null && !mClientSocket.isClosed()) {
                mClientSocket.close();
                Log.d(TAG, "ConnID: " + mConnectionId + " - Socket已强制关闭");
            }
        } catch (IOException e) {
            Log.w(TAG, "ConnID: " + mConnectionId + " - 强制关闭Socket时出错: " + e.getMessage());
        }
        
        // 停止监控器
        if (mHeartbeatMonitor != null) {
            mHeartbeatMonitor.stop();
        }
        if (mAppMonitor != null) {
            mAppMonitor.stop();
        }
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        Log.d(TAG, "ConnID: " + mConnectionId + " - 进入cleanup进行清理。");
        
        // 停止监控器
        mIsClientAlive = false;
        if (mHeartbeatMonitor != null) {
            mHeartbeatMonitor.stop();
        }
        if (mAppMonitor != null) {
            mAppMonitor.stop();
        }
        
        // 发送断开通知
        if (mWriter != null && !mWriter.checkError()) {
            try {
                mWriter.println("SERVER_SHUTDOWN:Service_Stopping");
                mWriter.flush();
                Log.d(TAG, "ConnID: " + mConnectionId + " - 已发送服务器关闭通知");
            } catch (Exception e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 发送关闭通知失败: " + e.getMessage());
            }
        }
        
        // 清理VD相关资源
        if (mRequestProcessor != null) {
            mRequestProcessor.cleanup(mPendingVDRequest, mReusedManualVD, mConnectionId);
        }
        
        // 关闭IO流和Socket
        closeResources();
        
        // 从服务器管理器中移除
        mNetworkServerManager.removeClientHandler(this);
        
        Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 执行完毕并清理。");
    }
    
    /**
     * 关闭资源
     */
    private void closeResources() {
        try {
            if (mWriter != null) mWriter.close();
        } catch (Exception e) { /* ignore */ }
        try {
            if (mReader != null) mReader.close();
        } catch (Exception e) { /* ignore */ }
        try {
            if (mClientSocket != null && !mClientSocket.isClosed()) mClientSocket.close();
        } catch (IOException e) {
            Log.e(TAG, "关闭客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 的Socket时出错: " + e.getMessage());
        }
    }
    
    /**
     * 客户端请求数据类
     */
    public static class ClientRequest {
        public final String userIdentifier;
        public final boolean forceCreateNew;
        public final String targetAppPackageName;
        public final String targetAppActivityName;
        
        public ClientRequest(String userIdentifier, boolean forceCreateNew, 
                           String targetAppPackageName, String targetAppActivityName) {
            this.userIdentifier = userIdentifier;
            this.forceCreateNew = forceCreateNew;
            this.targetAppPackageName = targetAppPackageName;
            this.targetAppActivityName = targetAppActivityName;
        }
    }
}
