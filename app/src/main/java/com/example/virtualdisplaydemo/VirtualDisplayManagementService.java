package com.example.virtualdisplaydemo;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.media.projection.MediaProjection;
import android.os.Binder;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.example.virtualdisplaydemo.manager.VirtualDisplayManager;
import com.example.virtualdisplaydemo.manager.NetworkServerManager;
import com.example.virtualdisplaydemo.manager.ClientConnectionManager;
import com.example.virtualdisplaydemo.manager.AppLaunchManager;
import com.example.virtualdisplaydemo.isolation.ProcessIsolationManager;
import com.example.virtualdisplaydemo.model.VirtualDisplayContainer;
import com.example.virtualdisplaydemo.callback.ActivityCallback;

public class VirtualDisplayManagementService extends Service {
    private static final String TAG = "VDMService_Smart_CN";
    private static final String NOTIFICATION_CHANNEL_ID = "VDMServiceChannel_Smart_CN";
    private static final int SERVICE_NOTIFICATION_ID = 105;

    // 管理器组件
    private VirtualDisplayManager mVirtualDisplayManager;
    private NetworkServerManager mNetworkServerManager;
    private ClientConnectionManager mConnectionManager;
    private AppLaunchManager mAppLaunchManager;
    private ProcessIsolationManager mProcessIsolationManager;

    // 线程处理
    private HandlerThread mServiceHandlerThread;
    private Handler mServiceHandler;
    private Handler mMainThreadHandler;

    // 服务绑定
    private final IBinder mBinder = new LocalBinder();

    /**
     * 本地绑定器 - 提供服务访问接口
     */
    public class LocalBinder extends Binder {
        public VirtualDisplayManagementService getService() {
            return VirtualDisplayManagementService.this;
        }

        public void setActivityCallback(@Nullable ActivityCallback callback) {
            if (mNetworkServerManager != null) {
                mNetworkServerManager.setActivityCallback(callback);
            }
        }
    }

    /**
     * 获取进程隔离管理器
     */
    public ProcessIsolationManager getProcessIsolationManager() {
        return mProcessIsolationManager;
    }

    /**
     * 停止所有网络连接
     */
    public void stopAllNetworkConnections() {
        if (mNetworkServerManager != null) {
            mNetworkServerManager.stopAllNetworkConnections();
        }
    }

    /**
     * 服务生命周期方法
     */
    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化线程处理
        mServiceHandlerThread = new HandlerThread(TAG + "_SmartBgThread");
        mServiceHandlerThread.start();
        mServiceHandler = new Handler(mServiceHandlerThread.getLooper());
        mMainThreadHandler = new Handler(Looper.getMainLooper());

        // 初始化管理器组件
        initializeManagers();

        // 创建通知并启动前台服务
        createNotificationChannel();
        startForeground(SERVICE_NOTIFICATION_ID, createServiceNotification());

        // 暂时禁用内存监控启动，避免闪退
        // mVirtualDisplayManager.startMemoryMonitoring();

        // 启动网络服务器
        mNetworkServerManager.startServerListener();

        Log.i(TAG, "DSMS服务已创建并在前台运行。");
    }

    /**
     * 初始化所有管理器组件
     */
    private void initializeManagers() {
        // 临时使用简化版虚拟显示管理器避免闪退
        // mVirtualDisplayManager = VirtualDisplayManager.getInstance(this);
        // 暂时注释掉优化版本，使用原始版本
        mVirtualDisplayManager = VirtualDisplayManager.getInstance(this);

        // 初始化连接管理器
        mConnectionManager = new ClientConnectionManager();

        // 初始化应用启动管理器
        mAppLaunchManager = new AppLaunchManager(this, mServiceHandler, mMainThreadHandler);

        // 初始化进程隔离管理器
        mProcessIsolationManager = new ProcessIsolationManager(this);

        // 初始化网络服务器管理器
        mNetworkServerManager = new NetworkServerManager(mServiceHandler,
                                                        mMainThreadHandler,
                                                        mVirtualDisplayManager,
                                                        mConnectionManager,
                                                        mAppLaunchManager);

        Log.d(TAG, "所有管理器组件已初始化完成");
    }

    /**
     * 公共接口方法 - 供MainActivity调用
     */

    /**
     * 创建虚拟显示 - 供MainActivity调用
     */
    public @Nullable VirtualDisplayContainer createVirtualDisplayForClientWithProjection(
            int internalClientId, @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, @NonNull String initialUserIdentifier, boolean isManual) {

        return mVirtualDisplayManager.createVirtualDisplay(internalClientId, targetSurface,
                                                          mediaProjection, initialUserIdentifier, isManual);
    }

    /**
     * 释放虚拟显示 - 供MainActivity调用
     */
    public void releaseVirtualDisplay(int internalClientId, boolean calledFromSurfaceDestroyed) {
        mServiceHandler.post(() -> {
            mVirtualDisplayManager.releaseVirtualDisplay(internalClientId);
        });
    }

    /**
     * 检查显示是否被网络客户端使用
     */
    public boolean isDisplayBeingUsedByNetworkClient(int internalClientId) {
        return mConnectionManager.isDisplayBeingUsedByNetworkClient(internalClientId);
    }

    /**
     * 获取显示的网络用户信息
     */
    public @Nullable String getDisplayNetworkUserInfo(int internalClientId) {
        return mConnectionManager.getDisplayNetworkUserInfo(internalClientId);
    }

    /**
     * 通知虚拟显示处理完成
     */
    public void notifyVirtualDisplayProcessed(String serviceRequestId, int internalClientId, @Nullable String displayIdOrError) {
        mConnectionManager.notifyVirtualDisplayProcessed(serviceRequestId, internalClientId, displayIdOrError);
    }

    /**
     * 通知MediaProjection被拒绝
     */
    public void notifyMediaProjectionDenied(String serviceRequestId) {
        mConnectionManager.notifyMediaProjectionDenied(serviceRequestId);
    }

    /**
     * 在MediaProjection权限获取后重试请求
     */
    public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientIdFromActivity) {
        Log.d(TAG, "retryRequestAfterMediaProjection called for: " + serviceRequestId);
        mNetworkServerManager.retryRequestAfterMediaProjection(serviceRequestId, userIdentifier, assignedInternalClientIdFromActivity);
    }

    /**
     * 通知MediaProjection请求繁忙
     */
    public void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId) {
        mConnectionManager.notifyMediaProjectionRequestBusy(newRequestId, existingRequestId);
    }

    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG, "服务被绑定");
        return mBinder;
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "DSMS服务正在销毁...");

        // 暂时禁用内存监控停止
        // mVirtualDisplayManager.stopMemoryMonitoring();

        // 清理进程隔离管理器
        if (mProcessIsolationManager != null) {
            mProcessIsolationManager.cleanup();
        }

        // 停止所有网络连接
        stopAllNetworkConnections();

        // 停止服务线程
        if (mServiceHandlerThread != null) {
            mServiceHandlerThread.quitSafely();
            try {
                mServiceHandlerThread.join(3000);
            } catch (InterruptedException e) {
                Log.w(TAG, "等待服务线程结束被中断");
            }
        }

        super.onDestroy();
        Log.i(TAG, "DSMS服务已销毁");
    }

    /**
     * 通知相关方法
     */
    private void createNotificationChannel() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "虚拟显示管理服务",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("管理虚拟显示和网络连接的后台服务");

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createServiceNotification() {
        Intent notificationIntent = new Intent(this, getClass());
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("虚拟显示管理服务")
            .setContentText("正在运行虚拟显示和网络服务")
            .setSmallIcon(android.R.drawable.ic_menu_manage)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build();
    }
}