package com.example.virtualdisplaydemo.strategy;

import java.io.PrintWriter;

/**
 * 应用启动策略接口 - 定义不同的应用启动方式
 */
public interface AppLaunchStrategy {
    
    /**
     * 启动应用
     */
    void launchApp(String packageName, String activityName, String displayId, 
                   String requestId, String connectionId, LaunchCallback callback, PrintWriter writer);
    
    /**
     * 启动回调接口
     */
    interface LaunchCallback {
        void onLaunchSuccess(String requestId);
        void onLaunchFailed(String requestId, String reason, boolean canRetryWithAdb);
    }
}
