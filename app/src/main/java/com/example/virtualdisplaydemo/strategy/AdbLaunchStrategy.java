package com.example.virtualdisplaydemo.strategy;

import android.os.Handler;
import android.util.Log;

import java.io.PrintWriter;

/**
 * ADB启动策略 - 通过ADB命令启动应用（返回命令给客户端执行）
 */
public class AdbLaunchStrategy implements AppLaunchStrategy {
    private static final String TAG = "AdbLaunchStrategy";
    
    private final Handler mServiceHandler;
    
    public AdbLaunchStrategy(Handler serviceHandler) {
        mServiceHandler = serviceHandler;
    }
    
    @Override
    public void launchApp(String packageName, String activityName, String displayId, 
                         String requestId, String connectionId, LaunchCallback callback, PrintWriter writer) {
        
        mServiceHandler.post(() -> {
            try {
                String fullActivityName = activityName.startsWith(".") ? packageName + activityName : activityName;
                String adbCommand = "adb shell am start --display-id " + displayId + " -n " +
                                  packageName + "/" + fullActivityName;
                
                Log.i(TAG, "ConnID: " + connectionId + " - 发送ADB命令给客户端: " + adbCommand);
                
                if (writer != null && !writer.checkError()) {
                    writer.println("APP_LAUNCH_STATUS:" + requestId + ":NEED_ADB:" + adbCommand);
                    callback.onLaunchSuccess(requestId);
                } else {
                    callback.onLaunchFailed(requestId, "Writer不可用", false);
                }
                
            } catch (Exception e) {
                Log.e(TAG, "ConnID: " + connectionId + " - ADB命令生成失败: " + e.getMessage());
                callback.onLaunchFailed(requestId, "ADB命令生成失败: " + e.getMessage(), false);
            }
        });
    }
}
