package com.example.virtualdisplaydemo.strategy;

import android.app.ActivityOptions;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import java.io.PrintWriter;

/**
 * Intent启动策略 - 使用Android Intent机制启动应用
 */
public class IntentLaunchStrategy implements AppLaunchStrategy {
    private static final String TAG = "IntentLaunchStrategy";
    
    private final Context mContext;
    private final Handler mServiceHandler;
    private final Handler mMainThreadHandler;
    
    public IntentLaunchStrategy(Context context, Handler serviceHandler, Handler mainThreadHandler) {
        mContext = context;
        mServiceHandler = serviceHandler;
        mMainThreadHandler = mainThreadHandler;
    }
    
    @Override
    public void launchApp(String packageName, String activityName, String displayId,
                         String requestId, String connectionId, LaunchCallback callback, PrintWriter writer) {

        // 普通应用没有权限在虚拟显示器上启动其他应用，直接跳过Intent方式
        Log.w(TAG, "ConnID: " + connectionId + " - Intent方式需要系统权限，跳过并使用ADB方式");
        callback.onLaunchFailed(requestId, "Intent方式需要系统权限，使用ADB方式", true);
    }
    
    /**
     * 创建启动Intent
     */
    private Intent createLaunchIntent(String packageName, String activityName) {
        Intent launchIntent;
        String fullActivityName = activityName.startsWith(".") ? packageName + activityName : activityName;
        
        // 尝试使用PackageManager获取启动Intent
        PackageManager pm = mContext.getPackageManager();
        try {
            launchIntent = pm.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                Log.d(TAG, "使用PackageManager获取的启动Intent");
            } else {
                // 如果获取不到，手动创建
                launchIntent = new Intent();
                launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
                Log.d(TAG, "手动创建Intent: " + fullActivityName);
            }
        } catch (Exception e) {
            // 如果PackageManager失败，手动创建
            launchIntent = new Intent();
            launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
            Log.d(TAG, "PackageManager失败，手动创建Intent: " + fullActivityName);
        }
        
        return launchIntent;
    }
    
    /**
     * 创建ActivityOptions
     */
    private ActivityOptions createActivityOptions(String displayId) {
        ActivityOptions options = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            options = ActivityOptions.makeBasic();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && options != null) {
            options.setLaunchDisplayId(Integer.parseInt(displayId));
        }
        return options;
    }
}
