plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.example.virtualdisplaydemo"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.virtualdisplaydemo"
        minSdk = 21
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

}

//configurations.all {
//    resolutionStrategy {
//        force("org.jetbrains.kotlin:kotlin-stdlib:2.0.21")
//        force("org.jetbrains.kotlin:kotlin-stdlib-jdk8:2.0.21")
//        force("org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21")
//        force("org.jetbrains.kotlin:kotlin-stdlib-common:2.0.21")
//    }
//}

dependencies {
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.core.ktx)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}