#!/bin/bash

echo "🚀 开始终极修复Android Studio编译问题..."
echo ""

# 1. 完全清理所有Gradle缓存
echo "🗑️ 步骤1: 完全清理所有Gradle缓存..."
rm -rf ~/.gradle/caches
rm -rf ~/.gradle/daemon
rm -rf ~/.gradle/wrapper
rm -rf ~/.gradle/native
rm -rf ~/.gradle/jdks
rm -rf ~/.gradle/build-cache-*

# 2. 清理项目缓存
echo "🧹 步骤2: 清理项目缓存..."
rm -rf .gradle
rm -rf build
rm -rf app/build
rm -rf .idea/caches
rm -rf .idea/libraries

# 3. 备份并重新安装Android SDK 32
echo "📱 步骤3: 修复Android SDK..."
SDK_DIR="/Users/<USER>/Library/Android/sdk"

# 删除有问题的SDK版本
echo "删除Android SDK 33（有问题的版本）..."
rm -rf "$SDK_DIR/platforms/android-33"

# 检查SDK 32
if [ -d "$SDK_DIR/platforms/android-32" ]; then
    echo "✅ Android SDK 32 已安装"
else
    echo "⚠️ Android SDK 32 未安装，需要在Android Studio中安装"
fi

# 4. 创建最小化的gradle.properties
echo "📝 步骤4: 创建最小化的gradle.properties..."
cat > gradle.properties << 'EOF'
# Project-wide Gradle settings.
org.gradle.jvmargs=-Xmx4096m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.nonTransitiveRClass=true

# 使用Android Studio的JDK
org.gradle.java.home=/Applications/Android Studio.app/Contents/jbr/Contents/Home
EOF

# 5. 确保使用SDK 32
echo "📝 步骤5: 确保app/build.gradle.kts使用SDK 32..."
# 这已经在之前的步骤中完成

# 6. 清理并重建
echo "🔨 步骤6: 尝试清理构建..."
if [ -f "gradlew" ]; then
    chmod +x gradlew
    ./gradlew clean --no-daemon --no-build-cache || true
fi

echo ""
echo "✅ 终极修复脚本执行完成！"
echo ""
echo "📌 重要步骤（必须执行）："
echo ""
echo "1. 打开 Android Studio"
echo "2. 选择 Tools → SDK Manager"
echo "3. 在 SDK Platforms 标签页："
echo "   - 确保 Android 12.0 (API 32) 已安装（打勾）"
echo "   - 确保 Android 13.0 (API 33) 已卸载（取消打勾）"
echo "   - 如果需要，点击 Apply 应用更改"
echo "4. 在 SDK Tools 标签页："
echo "   - 确保 Android SDK Build-Tools 32.0.0 已安装"
echo "   - 点击 Apply"
echo "5. 关闭SDK Manager"
echo "6. 选择 File → Invalidate Caches... → 勾选所有选项 → Invalidate and Restart"
echo "7. 重启后，选择 File → Sync Project with Gradle Files"
echo ""
echo "如果还有问题，请尝试："
echo "- 在Android Studio中：Build → Clean Project"
echo "- 然后：Build → Rebuild Project" 