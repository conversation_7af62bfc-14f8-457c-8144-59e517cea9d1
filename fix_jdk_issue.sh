#!/bin/bash

echo "🔧 开始修复Android Studio JDK编译问题..."

# 1. 清理Gradle缓存
echo "📦 步骤1: 清理Gradle缓存..."
rm -rf ~/.gradle/caches/transforms-3
rm -rf ~/.gradle/caches/modules-2
rm -rf ~/.gradle/caches/jars-*
rm -rf ~/.gradle/caches/build-cache-*

# 2. 清理项目本地缓存
echo "🧹 步骤2: 清理项目缓存..."
rm -rf .gradle
rm -rf build
rm -rf app/build

# 3. 清理Android Studio缓存（可选）
echo "🗑️ 步骤3: 清理已知的缓存目录..."
rm -rf ~/.gradle/daemon
rm -rf ~/.gradle/wrapper

# 4. 设置正确的JDK路径
echo "☕ 步骤4: 检查JDK配置..."
if [ -f "local.properties" ]; then
    echo "找到local.properties文件，检查JDK路径..."
    cat local.properties
fi

# 5. 重新下载Gradle Wrapper
echo "📥 步骤5: 重新配置Gradle Wrapper..."
if [ -f "gradlew" ]; then
    ./gradlew wrapper --gradle-version=8.0
fi

echo "✅ 修复脚本执行完成！"
echo ""
echo "📌 请按照以下步骤继续："
echo "1. 打开Android Studio"
echo "2. 选择 File -> Invalidate Caches and Restart"
echo "3. 重启后选择 File -> Sync Project with Gradle Files"
echo "4. 如果仍有问题，请检查以下设置："
echo "   - File -> Project Structure -> SDK Location"
echo "   - 确保使用Android Studio内置的JDK (通常是JDK 17)"
echo "   - File -> Settings -> Build, Execution, Deployment -> Build Tools -> Gradle"
echo "   - 选择使用 'Gradle JDK: Android Studio default JDK'" 