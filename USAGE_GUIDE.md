# 混合虚拟屏幕系统使用指南

## 🚀 快速开始

### 1. 编译和运行

#### 解决Java版本问题
如果遇到Java版本错误，有以下解决方案：

**方案A: 升级到Java 17（推荐）**
```bash
# 安装Java 17
brew install openjdk@17

# 设置JAVA_HOME
export JAVA_HOME=/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home

# 重新编译
./gradlew clean assembleDebug
```

**方案B: 降级Android Gradle Plugin**
```kotlin
// 在 build.gradle.kts 中修改
plugins {
    id("com.android.application") version "7.4.2"  // 降级版本
}
```

**方案C: 使用Android Studio**
- 打开Android Studio
- File -> Open -> 选择项目目录
- Android Studio会自动处理Java版本问题

### 2. 基本使用流程

#### 步骤1: 启动应用
```bash
# 安装到设备
adb install app/build/outputs/apk/debug/app-debug.apk

# 启动应用
adb shell am start -n com.example.virtualdisplaydemo/.MainActivity
```

#### 步骤2: 授予权限
1. 应用启动后会请求屏幕录制权限
2. 点击"允许"授予MediaProjection权限
3. 应用会显示"屏幕捕获权限已授予"

#### 步骤3: 创建虚拟屏幕
**方法A: 手动创建**
1. 点击"Add Virtual Screen"按钮
2. 系统会创建一个虚拟屏幕
3. 在屏幕上会显示虚拟显示器的状态

**方法B: 通过网络创建**
1. 运行Python客户端连接到应用
2. 应用会自动创建虚拟屏幕
3. 在虚拟屏幕上启动目标应用

### 3. Python客户端使用

#### 基本连接示例
```python
import socket
import json
import time

def connect_to_virtual_display():
    # 连接到Android应用
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    try:
        # 连接到设备IP（需要替换为实际IP）
        client.connect(('*************', 12345))
        
        # 发送启动应用请求
        request = {
            "action": "launch_app",
            "package_name": "com.example.testapp",
            "activity_name": "MainActivity"
        }
        
        client.send(json.dumps(request).encode())
        
        # 接收响应
        response = client.recv(1024).decode()
        print(f"服务器响应: {response}")
        
        # 保持连接
        while True:
            time.sleep(1)
            
    except Exception as e:
        print(f"连接错误: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    connect_to_virtual_display()
```

## 📱 界面说明

### 主界面元素

1. **"Add Virtual Screen"按钮**: 手动创建虚拟屏幕
2. **性能状态栏**: 显示系统性能信息
   ```
   Performance Status: Memory: 65% | Available: 1024MB | Sessions: 3 | Session Memory: 450MB | Health: 85
   ```
3. **虚拟屏幕列表**: 显示所有活跃的虚拟屏幕

### 虚拟屏幕状态指示

- 🟢 **绿色**: 应用正在运行
- 🟡 **黄色**: 网络客户端已连接
- ⏳ **沙漏**: 预占用状态
- ⚪ **白色**: 手动创建的空闲屏幕
- ⚫ **黑色**: 网络创建的空闲屏幕

### 性能状态颜色

- 🟢 **绿色**: 健康度 ≥ 80（系统运行良好）
- 🟡 **橙色**: 健康度 60-79（需要关注）
- 🔴 **红色**: 健康度 < 60（需要优化）

## 🔧 功能特性

### 1. 自动故障恢复
- 系统每30秒检查会话健康状态
- 检测到故障时自动重试（最多3次）
- 重试失败后自动清理资源

### 2. 智能资源管理
- 单个会话内存限制：200MB
- 系统内存使用率限制：85%
- 会话超时时间：10分钟
- 自动垃圾回收

### 3. 实时性能监控
- 内存使用率监控
- 活跃会话数量统计
- 系统健康分数计算
- 每5分钟生成性能报告

### 4. 进程级隔离
- 每个会话独立的线程池
- 独立的资源监控
- 故障隔离（一个会话崩溃不影响其他）
- 独立的生命周期管理

## 🎯 使用场景

### 场景1: 单人调试
```bash
1. 启动应用
2. 点击"Add Virtual Screen"
3. 在虚拟屏幕上手动启动目标应用
4. 进行调试工作
```

### 场景2: 多人协作调试
```bash
1. 启动应用（作为服务器）
2. 多个开发者通过Python客户端连接
3. 每个连接自动创建独立的虚拟屏幕
4. 在各自的虚拟屏幕上调试不同应用
```

### 场景3: 自动化测试
```python
# 自动化脚本示例
def automated_testing():
    # 连接到虚拟显示服务
    client = connect_to_server()
    
    # 启动测试应用
    launch_app(client, "com.test.app")
    
    # 执行测试用例
    run_test_cases(client)
    
    # 收集结果
    collect_results(client)
    
    # 清理资源
    client.close()
```

## 🚨 故障排除

### 常见问题

#### 1. 编译错误
```bash
# Java版本问题
Error: Android Gradle plugin requires Java 17

解决方案:
- 升级到Java 17
- 或使用Android Studio
- 或降级Android Gradle Plugin版本
```

#### 2. 权限问题
```bash
# MediaProjection权限被拒绝
Error: 屏幕捕获权限被拒绝

解决方案:
- 重新启动应用
- 在权限对话框中点击"允许"
- 检查系统设置中的应用权限
```

#### 3. 连接问题
```bash
# 网络连接失败
Error: Connection refused

解决方案:
- 检查设备IP地址
- 确保设备在同一网络
- 检查防火墙设置
- 确保应用正在运行
```

#### 4. 性能问题
```bash
# 内存使用过高
Warning: 内存使用率过高: 90%

解决方案:
- 减少并发会话数量
- 清理长时间运行的会话
- 重启应用释放内存
```

### 调试技巧

#### 1. 查看日志
```bash
# 查看应用日志
adb logcat | grep "DSMS\|HybridVirtualScreen\|PerformanceMonitor"

# 查看特定标签
adb logcat -s "DSMS_MainActivity"
```

#### 2. 监控性能
- 观察主界面的性能状态栏
- 健康度低于80时需要关注
- 内存使用率超过85%时会限制新会话

#### 3. 手动清理
```bash
# 如果遇到问题，可以：
1. 点击虚拟屏幕的"移除"按钮
2. 重启应用
3. 清理应用数据: adb shell pm clear com.example.virtualdisplaydemo
```

## 📊 性能优化建议

### 1. 内存优化
- 避免同时运行过多会话（建议≤5个）
- 定期清理不用的会话
- 监控单个会话内存使用（建议≤200MB）

### 2. 网络优化
- 使用稳定的WiFi连接
- 避免频繁的连接断开
- 实现客户端重连机制

### 3. 系统优化
- 关闭不必要的后台应用
- 确保设备有足够的可用内存（建议≥2GB）
- 定期重启设备清理系统缓存

## 📝 总结

混合虚拟屏幕系统提供了：

1. **更好的隔离性**: 每个调试会话独立运行
2. **智能资源管理**: 自动监控和优化内存使用
3. **自动故障恢复**: 检测并自动恢复故障会话
4. **全面的监控**: 实时监控系统和会话性能
5. **易于使用**: 简单的界面和API

通过合理使用，该系统可以支持多人协作调试，同时保持良好的性能和稳定性。
