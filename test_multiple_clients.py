#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多客户端测试脚本 - 优化版本
用于测试虚拟显示器管理服务的多客户端并发处理能力

优化内容：
1. 改进连接稳定性测试
2. 添加详细的状态监控
3. 优化错误处理和恢复
4. 支持更多测试场景
"""

import subprocess
import time
import threading
import sys
import signal
import os
import socket
import json

class MultiClientTester:
    def __init__(self):
        self.processes = []
        self.running = True
        self.client_stats = {}  # 客户端统计信息
        self.start_time = time.time()

        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        _ = frame  # 忽略未使用的参数
        print(f"\n🛑 收到信号 {signum}，正在停止所有客户端...")
        self.stop_all_clients()

    def stop_all_clients(self):
        self.running = False
        for process in self.processes:
            if process.poll() is None:  # 进程还在运行
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"停止进程时出错: {e}")
        self.processes.clear()

    def start_client(self, client_id, package_name, activity_name, port_offset=0, server_ip="localhost"):
        """启动一个客户端 - 增强版本"""
        try:
            # 准备输入数据（使用优化后的格式）
            port = 30000 + port_offset
            input_data = f"{server_ip}\n{port}\n{package_name}\n{activity_name}\n1\ny\n"

            print(f"🚀 [客户端 {client_id}] 启动中... (端口: {port})")

            # 记录客户端统计信息
            self.client_stats[client_id] = {
                'start_time': time.time(),
                'package': package_name,
                'activity': activity_name,
                'port': port,
                'status': 'starting',
                'messages': [],
                'errors': 0,
                'last_activity': time.time()
            }

            # 启动客户端进程
            process = subprocess.Popen(
                ['python3', 'dct_client_refactored.py'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # 发送输入数据
            try:
                process.stdin.write(input_data)
                process.stdin.flush()
                process.stdin.close()
            except Exception as e:
                print(f"❌ [客户端 {client_id}] 发送输入数据失败: {e}")
                return None

            self.processes.append(process)
            self.client_stats[client_id]['status'] = 'running'

            # 启动输出监控线程
            output_thread = threading.Thread(
                target=self.monitor_client_output,
                args=(client_id, process),
                daemon=True
            )
            output_thread.start()

            print(f"✅ [客户端 {client_id}] 启动成功")
            return process

        except Exception as e:
            print(f"❌ [客户端 {client_id}] 启动失败: {e}")
            if client_id in self.client_stats:
                self.client_stats[client_id]['status'] = 'failed'
                self.client_stats[client_id]['errors'] += 1
            return None

    def monitor_client_output(self, client_id, process):
        """监控客户端输出 - 增强版本"""
        try:
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    timestamp = time.strftime("%H:%M:%S")

                    # 更新客户端统计信息
                    if client_id in self.client_stats:
                        self.client_stats[client_id]['last_activity'] = time.time()
                        self.client_stats[client_id]['messages'].append(line)

                        # 只保留最近的10条消息
                        if len(self.client_stats[client_id]['messages']) > 10:
                            self.client_stats[client_id]['messages'].pop(0)

                        # 检测错误
                        if "❌" in line or "ERROR" in line.upper() or "失败" in line:
                            self.client_stats[client_id]['errors'] += 1
                            self.client_stats[client_id]['status'] = 'error'
                        elif "✅" in line or "SUCCESS" in line.upper() or "成功" in line:
                            self.client_stats[client_id]['status'] = 'connected'

                    print(f"[{timestamp}] 📱 [客户端 {client_id}] {line}")
                else:
                    break

            # 进程结束
            if client_id in self.client_stats:
                self.client_stats[client_id]['status'] = 'finished'

        except Exception as e:
            print(f"❌ [客户端 {client_id}] 输出监控异常: {e}")
            if client_id in self.client_stats:
                self.client_stats[client_id]['status'] = 'error'
                self.client_stats[client_id]['errors'] += 1

    def test_sequential_clients(self):
        """测试顺序启动多个客户端"""
        print("=== 顺序启动测试 ===")
        
        test_apps = [
            # ("com.android.settings", ".Settings"),
            ("com.wsy.crashcatcher", ".MainActivity"),
            ("xcrash.sample", ".MainActivity"),
            ("com.android.calculator2", ".Calculator"),
            ("com.android.calendar", ".AllInOneActivity"),
        ]
        
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
                
            print(f"\n启动客户端 {i+1}: {package}")
            client = self.start_client(i+1, package, activity, i)
            
            if client:
                # 等待一段时间让客户端建立连接
                time.sleep(5)
            else:
                print(f"客户端 {i+1} 启动失败")

    def test_concurrent_clients(self):
        """测试并发启动多个客户端"""
        print("=== 并发启动测试 ===")
        
        test_apps = [
            ("com.android.settings", ".Settings"),
            ("com.android.calculator2", ".Calculator"),
            ("com.android.calendar", ".AllInOneActivity"),
        ]
        
        # 同时启动所有客户端
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
                
            print(f"启动客户端 {i+1}: {package}")
            self.start_client(i+1, package, activity, i)
            time.sleep(1)  # 短暂延迟避免端口冲突

    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "="*60)
        print("📊 客户端统计信息")
        print("="*60)

        total_clients = len(self.client_stats)
        running_clients = sum(1 for stats in self.client_stats.values() if stats['status'] in ['running', 'connected'])
        error_clients = sum(1 for stats in self.client_stats.values() if stats['status'] == 'error')
        finished_clients = sum(1 for stats in self.client_stats.values() if stats['status'] == 'finished')

        print(f"总客户端数: {total_clients}")
        print(f"运行中: {running_clients}")
        print(f"已完成: {finished_clients}")
        print(f"错误: {error_clients}")
        print(f"运行时间: {time.time() - self.start_time:.1f}秒")

        print("\n📋 详细状态:")
        for client_id, stats in self.client_stats.items():
            uptime = time.time() - stats['start_time']
            status_icon = {
                'starting': '🔄',
                'running': '🟡',
                'connected': '🟢',
                'error': '🔴',
                'finished': '⚪',
                'failed': '❌'
            }.get(stats['status'], '❓')

            print(f"  {status_icon} 客户端 {client_id}: {stats['status']} "
                  f"(运行 {uptime:.1f}s, 错误 {stats['errors']})")

    def test_stability(self, duration_minutes=5):
        """稳定性测试"""
        print(f"🧪 稳定性测试 - 运行 {duration_minutes} 分钟")

        test_apps = [
            ("com.android.settings", ".Settings"),
            ("com.wsy.crashcatcher", ".MainActivity"),
        ]

        # 启动客户端
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
            self.start_client(i+1, package, activity, i)
            time.sleep(2)

        # 运行指定时间
        end_time = time.time() + (duration_minutes * 60)
        last_stats_time = time.time()

        while self.running and time.time() < end_time:
            time.sleep(5)

            # 每30秒打印一次统计
            if time.time() - last_stats_time > 30:
                self.print_statistics()
                last_stats_time = time.time()

        print(f"\n✅ 稳定性测试完成")
        self.print_statistics()

    def wait_for_completion(self):
        """等待所有客户端完成 - 增强版本"""
        try:
            last_stats_time = time.time()

            while self.running and any(p.poll() is None for p in self.processes):
                time.sleep(1)

                # 每30秒打印一次统计
                if time.time() - last_stats_time > 30:
                    self.print_statistics()
                    last_stats_time = time.time()

        except KeyboardInterrupt:
            print("\n🛑 用户中断测试")
        finally:
            self.stop_all_clients()
            self.print_statistics()

def main():
    if len(sys.argv) < 2:
        print("🧪 多客户端测试工具 - 优化版本")
        print("="*50)
        print("用法:")
        print("  python3 test_multiple_clients.py sequential   # 顺序启动测试")
        print("  python3 test_multiple_clients.py concurrent   # 并发启动测试")
        print("  python3 test_multiple_clients.py stability    # 稳定性测试 (5分钟)")
        print("  python3 test_multiple_clients.py stability 10 # 稳定性测试 (10分钟)")
        print("\n特性:")
        print("  ✅ 改进的连接稳定性")
        print("  ✅ 详细的统计信息")
        print("  ✅ 实时状态监控")
        print("  ✅ 错误检测和恢复")
        sys.exit(1)

    test_mode = sys.argv[1].lower()

    print("🧪 多客户端测试开始")
    print(f"📋 测试模式: {test_mode}")
    print("🛑 按 Ctrl+C 停止测试")
    print("-" * 50)

    tester = MultiClientTester()

    try:
        if test_mode == "sequential":
            tester.test_sequential_clients()
            print("\n⏳ 所有客户端已启动，等待完成...")
            tester.wait_for_completion()

        elif test_mode == "concurrent":
            tester.test_concurrent_clients()
            print("\n⏳ 所有客户端已启动，等待完成...")
            tester.wait_for_completion()

        elif test_mode == "stability":
            duration = 5  # 默认5分钟
            if len(sys.argv) > 2:
                try:
                    duration = int(sys.argv[2])
                except ValueError:
                    print("⚠️ 无效的时间参数，使用默认值5分钟")

            tester.test_stability(duration)

        else:
            print(f"❌ 未知测试模式: {test_mode}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        tester.stop_all_clients()
        print("\n🏁 测试结束")

if __name__ == "__main__":
    main()
