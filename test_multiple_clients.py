#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多客户端测试脚本
用于测试虚拟显示器管理服务的多客户端并发处理能力
"""

import subprocess
import time
import threading
import sys
import signal
import os

class MultiClientTester:
    def __init__(self):
        self.processes = []
        self.running = True
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        print(f"\n收到信号 {signum}，正在停止所有客户端...")
        self.stop_all_clients()

    def stop_all_clients(self):
        self.running = False
        for process in self.processes:
            if process.poll() is None:  # 进程还在运行
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                except Exception as e:
                    print(f"停止进程时出错: {e}")
        self.processes.clear()

    def start_client(self, client_id, package_name, activity_name, port_offset=0):
        """启动一个客户端"""
        try:
            # 准备输入数据
            input_data = f"localhost\n{30000 + port_offset}\n{package_name}\n{activity_name}\n1\ny\n"
            
            print(f"[客户端 {client_id}] 启动中...")
            
            # 启动客户端进程
            process = subprocess.Popen(
                ['python3', 'dct_client_refactored.py'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 发送输入数据
            process.stdin.write(input_data)
            process.stdin.flush()
            process.stdin.close()
            
            self.processes.append(process)
            
            # 启动输出监控线程
            output_thread = threading.Thread(
                target=self.monitor_client_output,
                args=(client_id, process),
                daemon=True
            )
            output_thread.start()
            
            return process
            
        except Exception as e:
            print(f"[客户端 {client_id}] 启动失败: {e}")
            return None

    def monitor_client_output(self, client_id, process):
        """监控客户端输出"""
        try:
            while self.running and process.poll() is None:
                line = process.stdout.readline()
                if line:
                    print(f"[客户端 {client_id}] {line.strip()}")
                else:
                    break
        except Exception as e:
            print(f"[客户端 {client_id}] 输出监控异常: {e}")

    def test_sequential_clients(self):
        """测试顺序启动多个客户端"""
        print("=== 顺序启动测试 ===")
        
        test_apps = [
            ("com.android.settings", ".Settings"),
            ("com.android.calculator2", ".Calculator"),
            ("com.android.calendar", ".AllInOneActivity"),
        ]
        
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
                
            print(f"\n启动客户端 {i+1}: {package}")
            client = self.start_client(i+1, package, activity, i)
            
            if client:
                # 等待一段时间让客户端建立连接
                time.sleep(5)
            else:
                print(f"客户端 {i+1} 启动失败")

    def test_concurrent_clients(self):
        """测试并发启动多个客户端"""
        print("=== 并发启动测试 ===")
        
        test_apps = [
            ("com.android.settings", ".Settings"),
            ("com.android.calculator2", ".Calculator"),
            ("com.android.calendar", ".AllInOneActivity"),
        ]
        
        # 同时启动所有客户端
        for i, (package, activity) in enumerate(test_apps):
            if not self.running:
                break
                
            print(f"启动客户端 {i+1}: {package}")
            self.start_client(i+1, package, activity, i)
            time.sleep(1)  # 短暂延迟避免端口冲突

    def wait_for_completion(self):
        """等待所有客户端完成"""
        try:
            while self.running and any(p.poll() is None for p in self.processes):
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断测试")
        finally:
            self.stop_all_clients()

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print("  python3 test_multiple_clients.py sequential  # 顺序启动测试")
        print("  python3 test_multiple_clients.py concurrent  # 并发启动测试")
        sys.exit(1)
    
    test_mode = sys.argv[1].lower()
    
    print("多客户端测试开始")
    print(f"测试模式: {test_mode}")
    print("按 Ctrl+C 停止测试")
    print("-" * 50)
    
    tester = MultiClientTester()
    
    try:
        if test_mode == "sequential":
            tester.test_sequential_clients()
        elif test_mode == "concurrent":
            tester.test_concurrent_clients()
        else:
            print(f"未知测试模式: {test_mode}")
            sys.exit(1)
        
        print("\n所有客户端已启动，等待完成...")
        tester.wait_for_completion()
        
    except Exception as e:
        print(f"测试异常: {e}")
    finally:
        tester.stop_all_clients()
        print("测试结束")

if __name__ == "__main__":
    main()
