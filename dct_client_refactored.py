#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DCT Client - 重构版本
虚拟显示器管理客户端，采用面向对象设计
"""

import socket
import subprocess
import threading
import time
import sys
import uuid
import signal
import atexit
import re
from typing import Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

class AppLaunchStatus(Enum):
    """应用启动状态枚举"""
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    NEED_ADB = "need_adb"

@dataclass
class ClientConfig:
    """客户端配置"""
    server_ip: str
    server_port: int
    target_package: str
    target_activity: str
    force_new_display: bool = False
    heartbeat_interval: int = 15
    heartbeat_timeout: int = 30
    connection_timeout: int = 30

    def __post_init__(self):
        """配置验证"""
        self.validate()

    def validate(self):
        """验证配置参数"""
        # IP地址验证
        if not self._is_valid_ip_or_hostname(self.server_ip):
            raise ValueError(f"无效的IP地址或主机名: {self.server_ip}")
        
        # 端口验证
        if not (1 <= self.server_port <= 65535):
            raise ValueError(f"端口号必须在1-65535之间: {self.server_port}")
        
        # 包名验证（必需）
        if not self.target_package or not self.target_package.strip():
            raise ValueError("应用包名不能为空")
        
        if not self._is_valid_package_name(self.target_package):
            raise ValueError(f"无效的包名格式: {self.target_package}")
        
        # Activity名验证（必需）
        if not self.target_activity or not self.target_activity.strip():
            raise ValueError("Activity名不能为空")

    def _is_valid_ip_or_hostname(self, address: str) -> bool:
        """验证IP地址或主机名"""
        if address.lower() in ['localhost', '127.0.0.1']:
            return True
        
        # IPv4地址验证
        ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if re.match(ipv4_pattern, address):
            parts = address.split('.')
            return all(0 <= int(part) <= 255 for part in parts)
        
        # 主机名验证（简单）
        hostname_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return bool(re.match(hostname_pattern, address))

    def _is_valid_package_name(self, package: str) -> bool:
        """验证Android包名格式"""
        package_pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$'
        return bool(re.match(package_pattern, package))

@dataclass
class ConnectionInfo:
    """连接信息"""
    status: ConnectionStatus = ConnectionStatus.DISCONNECTED
    supports_heartbeat: bool = False
    supports_app_launch: bool = False
    display_id: Optional[str] = None
    internal_id: Optional[str] = None
    app_launch_status: Optional[AppLaunchStatus] = None
    error_message: Optional[str] = None
    server_shutdown: bool = False
    target_app_exited: bool = False
    connection_closed: bool = False

class Logger:
    """日志管理器"""
    
    @staticmethod
    def log(message: str, level: str = "INFO"):
        """输出带时间戳的日志"""
        timestamp = time.strftime("%H:%M:%S")
        icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "ERROR": "❌",
            "WARNING": "⚠️",
            "DEBUG": "🔍"
        }
        icon = icons.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")

    @staticmethod
    def success(message: str):
        Logger.log(message, "SUCCESS")
    
    @staticmethod
    def error(message: str):
        Logger.log(message, "ERROR")
    
    @staticmethod
    def warning(message: str):
        Logger.log(message, "WARNING")
    
    @staticmethod
    def debug(message: str):
        Logger.log(message, "DEBUG")

class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def get_server_address() -> str:
        """获取并验证服务器地址"""
        while True:
            address = input("请输入DSMS服务器地址 (默认: localhost): ").strip()
            if not address:
                address = "localhost"
            
            try:
                # 简单验证
                if address.lower() in ['localhost', '127.0.0.1']:
                    return address
                
                # IPv4验证
                ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
                if re.match(ipv4_pattern, address):
                    parts = address.split('.')
                    if all(0 <= int(part) <= 255 for part in parts):
                        return address
                    else:
                        Logger.error("IP地址格式错误，请重新输入")
                        continue
                
                # 主机名（简单验证）
                if re.match(r'^[a-zA-Z0-9\-\.]+$', address):
                    return address
                
                Logger.error("无效的地址格式，请重新输入")
            except ValueError:
                Logger.error("地址格式错误，请重新输入")

    @staticmethod
    def get_server_port() -> int:
        """获取并验证服务器端口"""
        import random
        default_port = random.randint(30000, 40000)
        
        while True:
            port_input = input(f"请输入PC转发端口 (默认: {default_port}): ").strip()
            if not port_input:
                return default_port
            
            try:
                port = int(port_input)
                if 1 <= port <= 65535:
                    return port
                else:
                    Logger.error("端口号必须在1-65535之间")
            except ValueError:
                Logger.error("请输入有效的数字端口号")

    @staticmethod
    def get_package_name() -> str:
        """获取并验证包名（必需）"""
        while True:
            package = input("请输入应用包名 (必需，如: com.android.settings): ").strip()
            if not package:
                Logger.error("包名不能为空，请重新输入")
                continue
            
            # 验证包名格式
            package_pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$'
            if re.match(package_pattern, package):
                return package
            else:
                Logger.error("包名格式不正确，应类似 com.example.app")

    @staticmethod
    def get_activity_name() -> str:
        """获取并验证Activity名（必需）"""
        while True:
            activity = input("请输入Activity名 (必需，如: .MainActivity): ").strip()
            if not activity:
                Logger.error("Activity名不能为空，请重新输入")
                continue
            return activity

    @staticmethod
    def get_display_preference() -> bool:
        """获取显示策略偏好"""
        print("\n📱 虚拟显示策略:")
        print("1. 智能复用 (推荐) - 优先使用现有显示")
        print("2. 强制新建 - 总是创建新的虚拟显示")
        
        while True:
            choice = input("请选择策略 (1-2, 默认: 1): ").strip()
            if not choice or choice == "1":
                return False  # 不强制新建
            elif choice == "2":
                return True   # 强制新建
            else:
                Logger.error("请输入1或2")

    @staticmethod
    def confirm_configuration(config: ClientConfig) -> bool:
        """确认配置 - 类似vaa.py的确认模式"""
        print("\n" + "="*50)
        print("📋 配置确认")
        print("="*50)
        print(f"🌐 服务器地址: {config.server_ip}:{config.server_port}")
        print(f"📱 目标应用: {config.target_package}")
        print(f"🎯 Activity: {config.target_activity}")
        print(f"🆕 强制新建显示: {'是' if config.force_new_display else '否'}")
        print("="*50)
        
        while True:
            confirm = input("\n确认以上配置并开始连接? (Y/n): ").strip().lower()
            if confirm in ['y', 'yes', '']:
                return True
            elif confirm in ['n', 'no']:
                return False
            else:
                Logger.error("请输入 y 或 n")

class ADBManager:
    """ADB管理器"""
    
    @staticmethod
    def check_connection(target_device: Optional[str] = None) -> bool:
        """检查ADB连接"""
        Logger.log("检查ADB连接...")
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                Logger.error(f"ADB命令失败: {result.stderr}")
                return False

            lines = result.stdout.strip().split('\n')
            device_lines = [line for line in lines if line.strip() and not line.lower().startswith('list of devices')]
            
            if not device_lines:
                Logger.error("未检测到ADB设备")
                return False

            active_devices = 0
            target_found = False
            
            for line in device_lines:
                parts = line.split()
                if len(parts) < 2:
                    continue
                    
                device_id, state = parts[0], parts[1].lower()
                if state in ["device", "emulator"]:
                    active_devices += 1
                    if target_device and target_device.lower() != "localhost":
                        if device_id == target_device:
                            target_found = True
                    else:
                        target_found = True

            if target_device and target_device.lower() != "localhost" and not target_found:
                Logger.error(f"未找到目标设备: {target_device}")
                return False
                
            if active_devices == 0:
                Logger.error("未检测到活动的ADB设备")
                return False

            Logger.success(f"ADB连接正常，找到{active_devices}个活动设备")
            return True

        except subprocess.TimeoutExpired:
            Logger.error("ADB命令执行超时")
            return False
        except FileNotFoundError:
            Logger.error("未找到ADB命令，请确保Android SDK已安装")
            return False
        except Exception as e:
            Logger.error(f"ADB检查异常: {e}")
            return False

    @staticmethod
    def setup_port_forwarding(local_port: int, remote_port: int, device: Optional[str] = None) -> bool:
        """设置端口转发"""
        Logger.log(f"设置端口转发: {local_port} -> {remote_port}")
        
        try:
            adb_cmd = ['adb']
            if device and device.lower() != "localhost":
                adb_cmd.extend(['-s', device])
            
            # 移除现有转发
            subprocess.run(adb_cmd + ['forward', '--remove', f'tcp:{local_port}'], 
                         capture_output=True, timeout=5)
            
            # 设置新转发
            result = subprocess.run(adb_cmd + ['forward', f'tcp:{local_port}', f'tcp:{remote_port}'],
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                Logger.success("端口转发设置成功")
                return True
            else:
                Logger.error(f"端口转发失败: {result.stderr}")
                return False
                
        except Exception as e:
            Logger.error(f"端口转发异常: {e}")
            return False

    @staticmethod
    def launch_app(package: str, activity: str, display_id: str) -> bool:
        """通过ADB启动应用"""
        try:
            # 构建完整Activity名
            if activity.startswith('.'):
                full_activity = package + activity
            else:
                full_activity = activity

            command = [
                'adb', 'shell', 'am', 'start',
                '--display', str(display_id),
                '-n', f'{package}/{full_activity}'
            ]

            Logger.log(f"启动应用: {package}/{full_activity} -> Display {display_id}")
            
            result = subprocess.run(command, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                Logger.success("应用启动成功")
                return True
            else:
                Logger.error(f"应用启动失败: {result.stderr}")
                return False
                
        except Exception as e:
            Logger.error(f"应用启动异常: {e}")
            return False

class DCTClient:
    """DCT客户端主类"""

    def __init__(self, config: ClientConfig):
        self.config = config
        self.connection_info = ConnectionInfo()
        self.client_id = f"PyDC_{uuid.uuid4().hex[:4]}"
        self.socket: Optional[socket.socket] = None
        self.stop_event = threading.Event()
        self.threads = []

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        atexit.register(self.cleanup)

    def _signal_handler(self, signum, frame):
        """信号处理"""
        _ = frame  # 忽略未使用的参数
        Logger.warning(f"收到信号 {signum}，正在退出...")
        self.cleanup()

    def cleanup(self):
        """清理资源"""
        if hasattr(self, '_cleanup_called'):
            return
        self._cleanup_called = True

        Logger.log("开始清理资源...")

        # 停止所有线程
        self.stop_event.set()

        for thread in self.threads:
            if thread and thread.is_alive():
                Logger.debug(f"等待线程 {thread.name} 结束...")
                thread.join(timeout=2)
                if thread.is_alive():
                    Logger.warning(f"线程 {thread.name} 未在超时内结束")

        # 关闭socket
        if self.socket:
            try:
                self.socket.close()
                Logger.success("Socket连接已关闭")
            except Exception as e:
                Logger.warning(f"关闭Socket时出错: {e}")

        Logger.log("客户端已退出")
        sys.exit(0)

    def connect(self) -> bool:
        """连接到服务器"""
        try:
            Logger.log(f"连接到 {self.config.server_ip}:{self.config.server_port}...")

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.config.server_ip, self.config.server_port))
            self.socket.settimeout(None)

            self.connection_info.status = ConnectionStatus.CONNECTED
            Logger.success("已连接到DSMS")
            return True

        except Exception as e:
            Logger.error(f"连接失败: {e}")
            self.connection_info.status = ConnectionStatus.ERROR
            self.connection_info.error_message = str(e)
            return False

    def start_receiver_thread(self):
        """启动消息接收线程"""
        receiver_thread = threading.Thread(
            target=self._receiver_thread_func,
            name="ReceiverThread",
            daemon=True
        )
        receiver_thread.start()
        self.threads.append(receiver_thread)

    def start_heartbeat_thread(self):
        """启动心跳线程"""
        if self.connection_info.supports_heartbeat:
            heartbeat_thread = threading.Thread(
                target=self._heartbeat_thread_func,
                name="HeartbeatThread",
                daemon=True
            )
            heartbeat_thread.start()
            self.threads.append(heartbeat_thread)
            Logger.success("心跳线程已启动")

    def _receiver_thread_func(self):
        """消息接收线程函数"""
        Logger.log("接收线程已启动")

        try:
            sock_file = self.socket.makefile('r', encoding='utf-8')

            while not self.stop_event.is_set():
                try:
                    line = sock_file.readline()
                    if not line:
                        Logger.log("服务端关闭连接")
                        self.connection_info.connection_closed = True
                        self.stop_event.set()
                        break

                    line = line.strip()
                    if not line:
                        continue

                    self._handle_server_message(line)

                except socket.timeout:
                    continue
                except socket.error as e:
                    if not self.stop_event.is_set():
                        Logger.error(f"Socket错误: {e}")
                        self.connection_info.connection_closed = True
                        self.stop_event.set()
                    break
                except Exception as e:
                    if not self.stop_event.is_set():
                        Logger.error(f"接收消息异常: {e}")
                        self.stop_event.set()
                    break

        except Exception as e:
            Logger.error(f"接收线程异常: {e}")
            self.stop_event.set()
        finally:
            Logger.debug("接收线程已结束")

    def _handle_server_message(self, message: str):
        """处理服务器消息"""
        Logger.log(f"收到服务端消息: {message}")

        if message.startswith("DSMS_CONNECTED:"):
            if "HB" in message:
                self.connection_info.supports_heartbeat = True
                Logger.success("服务端支持心跳协议")
            if "AppLaunch" in message:
                self.connection_info.supports_app_launch = True
                Logger.success("服务端支持应用启动")

        elif message.startswith("VD_PENDING:"):
            Logger.log("虚拟显示创建进行中...")

        elif message.startswith("VD_CREATED:"):
            parts = message.split(":", 2)
            if len(parts) >= 3:
                self.connection_info.internal_id = parts[1]
                self.connection_info.display_id = parts[2]
                Logger.success(f"虚拟显示创建成功! Display ID: {parts[2]}")

        elif message.startswith("VD_REUSED:"):
            parts = message.split(":", 2)
            if len(parts) >= 3:
                self.connection_info.internal_id = parts[1]
                self.connection_info.display_id = parts[2]
                Logger.success(f"复用虚拟显示成功! Display ID: {parts[2]}")

        elif message.startswith("APP_LAUNCH_STATUS:"):
            self._handle_app_launch_status(message)

        elif message == "DSMS_HEARTBEAT_PING":
            self._send_heartbeat_pong()

        elif message == "DSMS_HEARTBEAT_PONG":
            Logger.debug("收到心跳PONG响应")

        elif message.startswith("ERROR:"):
            Logger.error(f"服务端错误: {message}")
            self.connection_info.error_message = message
            self.stop_event.set()

        elif message.startswith("SERVER_SHUTDOWN:"):
            Logger.warning("服务端正在关闭")
            self.connection_info.server_shutdown = True
            self.stop_event.set()

        elif message.startswith("TARGET_APP_EXITED:"):
            app_name = message.split(":", 1)[1] if ":" in message else "目标应用"
            Logger.log(f"目标应用 {app_name} 已退出")
            self.connection_info.target_app_exited = True
            self.stop_event.set()

    def _handle_app_launch_status(self, message: str):
        """处理应用启动状态"""
        parts = message.split(":", 3)
        if len(parts) >= 3:
            status = parts[2]
            message_text = parts[3] if len(parts) > 3 else ""

            if status == "OK":
                Logger.success(f"应用启动成功: {message_text}")
                self.connection_info.app_launch_status = AppLaunchStatus.SUCCESS
            elif status == "NEED_ADB":
                Logger.log("服务端权限限制，客户端自动执行ADB启动...")
                self.connection_info.app_launch_status = AppLaunchStatus.NEED_ADB
                self._auto_launch_app()
            else:
                Logger.error(f"应用启动失败: {message_text}")
                self.connection_info.app_launch_status = AppLaunchStatus.FAILED

    def _auto_launch_app(self):
        """自动启动应用"""
        if self.connection_info.display_id:
            success = ADBManager.launch_app(
                self.config.target_package,
                self.config.target_activity,
                self.connection_info.display_id
            )
            if success:
                self.connection_info.app_launch_status = AppLaunchStatus.SUCCESS
            else:
                self.connection_info.app_launch_status = AppLaunchStatus.FAILED

    def _send_heartbeat_pong(self):
        """发送心跳PONG响应"""
        try:
            self.socket.sendall(b"PY_HEARTBEAT_PONG\n")
            Logger.debug("发送心跳PONG响应")
        except Exception as e:
            Logger.error(f"发送心跳PONG失败: {e}")

    def _heartbeat_thread_func(self):
        """心跳线程函数"""
        Logger.debug("心跳线程已启动")
        last_ping_time = time.time()

        try:
            while not self.stop_event.is_set():
                current_time = time.time()

                # 发送心跳PING
                if current_time - last_ping_time >= self.config.heartbeat_interval:
                    try:
                        self.socket.sendall(b"PY_HEARTBEAT_PING\n")
                        Logger.debug("发送心跳PING")
                        last_ping_time = current_time
                    except Exception as e:
                        Logger.error(f"心跳发送失败: {e}")
                        self.stop_event.set()
                        break

                # 检查超时
                if current_time - last_ping_time > self.config.heartbeat_timeout:
                    Logger.error("心跳超时，连接断开")
                    self.stop_event.set()
                    break

                time.sleep(1)

        except Exception as e:
            Logger.error(f"心跳线程异常: {e}")
            self.stop_event.set()
        finally:
            Logger.debug("心跳线程已结束")

    def send_vd_request(self) -> bool:
        """发送虚拟显示请求"""
        try:
            force_new_str = "force_new" if self.config.force_new_display else "reuse"
            request = f"REQUEST_VD_SMART:{self.client_id}:{force_new_str}:{self.config.target_package}:{self.config.target_activity}\n"

            Logger.log(f"发送请求: {request.strip()}")
            self.socket.sendall(request.encode('utf-8'))
            return True

        except Exception as e:
            Logger.error(f"发送请求失败: {e}")
            return False

    def wait_for_display_creation(self) -> bool:
        """等待虚拟显示创建"""
        start_time = time.time()

        while time.time() - start_time < self.config.connection_timeout:
            if self.stop_event.is_set():
                break
            if self.connection_info.display_id:
                return True
            if self.connection_info.error_message:
                return False
            time.sleep(0.1)

        Logger.error("虚拟显示创建超时")
        return False

    def run(self) -> bool:
        """运行客户端"""
        try:
            Logger.log(f"启动DCT客户端 ({self.client_id})")

            # 连接服务器
            if not self.connect():
                return False

            # 启动接收线程
            self.start_receiver_thread()

            # 等待欢迎消息
            time.sleep(0.5)

            # 发送VD请求
            if not self.send_vd_request():
                return False

            # 等待VD创建
            if not self.wait_for_display_creation():
                return False

            # 启动心跳线程
            self.start_heartbeat_thread()

            # 自动启动应用
            if (self.connection_info.supports_app_launch and
                self.config.target_package and
                self.config.target_activity):

                Logger.log("启动应用到虚拟显示器...")
                time.sleep(1)  # 给服务端一点时间
                self._auto_launch_app()

            # 保持连接
            Logger.log("保持连接中... (按 Ctrl+C 退出)")
            self._keep_alive()

            return True

        except KeyboardInterrupt:
            Logger.warning("用户中断连接")
            return True
        except Exception as e:
            Logger.error(f"客户端运行异常: {e}")
            return False
        finally:
            self.cleanup()

    def _keep_alive(self):
        """保持连接活跃"""
        try:
            while not self.stop_event.is_set():
                time.sleep(0.5)

                # 检查各种退出条件
                if self.connection_info.connection_closed:
                    Logger.log("检测到连接断开")
                    break
                if self.connection_info.server_shutdown:
                    Logger.log("检测到服务器关闭")
                    break
                if self.connection_info.target_app_exited:
                    Logger.log("检测到目标应用退出")
                    break

                # 检查线程状态
                for thread in self.threads:
                    if thread and not thread.is_alive():
                        Logger.warning(f"线程 {thread.name} 已结束")
                        self.stop_event.set()
                        break

        except KeyboardInterrupt:
            Logger.warning("用户中断")
            self.stop_event.set()

def get_user_configuration() -> Optional[ClientConfig]:
    """获取用户配置"""
    try:
        print("=" * 60)
        print("🚀 DCT 客户端配置向导")
        print("=" * 60)
        print("连接到虚拟显示器管理服务并启动应用")
        print()

        # 获取服务器地址
        server_ip = InputValidator.get_server_address()

        # 检查ADB连接
        if not ADBManager.check_connection(server_ip):
            if input("⚠️ ADB连接检查失败，是否继续? (y/N): ").strip().lower() != 'y':
                Logger.warning("因ADB连接问题退出")
                return None

        # 获取端口
        server_port = InputValidator.get_server_port()

        # 获取应用信息（必需）
        target_package = InputValidator.get_package_name()
        target_activity = InputValidator.get_activity_name()

        # 获取显示策略
        force_new_display = InputValidator.get_display_preference()

        # 创建配置对象
        config = ClientConfig(
            server_ip=server_ip,
            server_port=server_port,
            target_package=target_package,
            target_activity=target_activity,
            force_new_display=force_new_display
        )

        # 确认配置
        if not InputValidator.confirm_configuration(config):
            Logger.warning("用户取消操作")
            return None

        return config

    except Exception as e:
        Logger.error(f"配置获取失败: {e}")
        return None

def main():
    """主函数"""
    try:
        Logger.log("DCT 客户端启动")
        Logger.log(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 获取用户配置
        config = get_user_configuration()
        if not config:
            sys.exit(1)

        # 设置ADB端口转发
        if not ADBManager.setup_port_forwarding(config.server_port, 33012, config.server_ip):
            Logger.error("ADB端口转发设置失败")
            sys.exit(1)

        print()
        Logger.log("开始连接并请求虚拟显示器")
        print()

        # 创建并运行客户端
        client = DCTClient(config)
        success = client.run()

        if success:
            Logger.success("客户端运行完成")
        else:
            Logger.error("客户端运行失败")
            sys.exit(1)

    except KeyboardInterrupt:
        Logger.warning("用户中断程序")
    except Exception as e:
        Logger.error(f"程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
