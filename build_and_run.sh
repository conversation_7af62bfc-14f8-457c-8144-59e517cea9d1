#!/bin/bash

# 混合虚拟屏幕系统编译和运行脚本

echo "🚀 混合虚拟屏幕系统编译和运行脚本"
echo "=================================="

# 检查Java版本
echo "📋 检查Java版本..."
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
echo "当前Java版本: $java_version"

if [ "$java_version" -lt 17 ]; then
    echo "⚠️  警告: 当前Java版本为 $java_version，推荐使用Java 17"
    echo "解决方案:"
    echo "1. 安装Java 17: brew install openjdk@17"
    echo "2. 设置JAVA_HOME: export JAVA_HOME=/opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home"
    echo "3. 或者使用Android Studio打开项目"
    echo ""
    read -p "是否继续尝试编译? (y/n): " continue_build
    if [ "$continue_build" != "y" ]; then
        exit 1
    fi
fi

# 清理项目
echo "🧹 清理项目..."
./gradlew clean

# 编译项目
echo "🔨 编译项目..."
if ./gradlew assembleDebug; then
    echo "✅ 编译成功!"
else
    echo "❌ 编译失败!"
    echo ""
    echo "常见解决方案:"
    echo "1. 升级Java到17版本"
    echo "2. 使用Android Studio打开项目"
    echo "3. 检查网络连接（下载依赖）"
    exit 1
fi

# 检查设备连接
echo "📱 检查设备连接..."
device_count=$(adb devices | grep -v "List of devices" | grep -c "device")
if [ "$device_count" -eq 0 ]; then
    echo "❌ 没有检测到Android设备"
    echo "请确保:"
    echo "1. 设备已连接并开启USB调试"
    echo "2. 已安装adb工具"
    echo "3. 设备已授权调试"
    exit 1
else
    echo "✅ 检测到 $device_count 个设备"
fi

# 安装应用
echo "📦 安装应用..."
if adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    echo "✅ 应用安装成功!"
else
    echo "❌ 应用安装失败!"
    exit 1
fi

# 启动应用
echo "🚀 启动应用..."
adb shell am start -n com.example.virtualdisplaydemo/.MainActivity

echo ""
echo "🎉 应用已启动!"
echo ""
echo "📋 使用说明:"
echo "1. 应用启动后会请求屏幕录制权限，请点击'允许'"
echo "2. 点击'Add Virtual Screen'按钮创建虚拟屏幕"
echo "3. 观察性能状态栏了解系统状态"
echo "4. 可以通过Python客户端连接进行网络调试"
echo ""
echo "📊 监控命令:"
echo "查看日志: adb logcat | grep 'DSMS\\|HybridVirtualScreen\\|PerformanceMonitor'"
echo "查看性能: adb logcat -s 'DSMS_MainActivity'"
echo ""
echo "🔧 故障排除:"
echo "如果遇到问题，可以运行: adb shell pm clear com.example.virtualdisplaydemo"
