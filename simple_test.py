import socket
import time
import subprocess
import sys

def setup_adb_forward(local_port, remote_port):
    """设置ADB端口转发"""
    try:
        # 先移除可能存在的转发
        subprocess.run(["adb", "forward", "--remove", f"tcp:{local_port}"],
                       capture_output=True, check=False)

        # 设置新的转发
        result = subprocess.run(["adb", "forward", f"tcp:{local_port}", f"tcp:{remote_port}"],
                                capture_output=True, text=True, check=True)
        print(f"✅ ADB端口转发设置成功: localhost:{local_port} -> device:{remote_port}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ ADB端口转发失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 找不到adb命令，请确保ADB已安装并在PATH中")
        return False

def test_dsms_connection():
    LOCAL_PORT = 33012
    REMOTE_PORT = 33012

    # 设置端口转发
    if not setup_adb_forward(LOCAL_PORT, REMOTE_PORT):
        print("端口转发设置失败，退出")
        return

    time.sleep(1)  # 等待端口转发生效

    try:
        print(f"\n📡 连接到 localhost:{LOCAL_PORT}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 设置10秒超时
        sock.connect(("localhost", LOCAL_PORT))
        print("✅ 连接成功！")

        # 读取欢迎消息
        print("\n等待欢迎消息...")
        data = sock.recv(1024)
        welcome_msg = data.decode('utf-8').strip()
        print(f"📨 收到欢迎消息: {welcome_msg}")

        # 发送测试请求
        test_msg = "REQUEST_VD_SMART:TestClient:reuse:com.wsy.crashcatcher:.MainActivity\n"
        print(f"\n📤 发送测试消息: {test_msg.strip()}")
        sock.sendall(test_msg.encode('utf-8'))

        # 等待响应
        print("\n等待响应...")
        sock.settimeout(5)  # 5秒超时
        response = sock.recv(1024)
        response_msg = response.decode('utf-8').strip()
        print(f"📨 收到响应: {response_msg}")

        # 如果收到 VD_PENDING，继续等待
        if "VD_PENDING" in response_msg:
            print("收到 VD_PENDING，继续等待...")
            response = sock.recv(1024)
            response_msg = response.decode('utf-8').strip()
            print(f"📨 收到后续响应: {response_msg}")

    except socket.timeout:
        print("❌ 连接或接收超时")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝，请确保:")
        print("   1. Android设备已连接")
        print("   2. DSMS服务正在设备上运行")
        print("   3. 使用 'adb devices' 确认设备已连接")
    except Exception as e:
        print(f"❌ 发生错误: {type(e).__name__}: {e}")
    finally:
        sock.close()
        print("\n连接已关闭")

def check_adb_devices():
    """检查ADB设备连接状态"""
    try:
        result = subprocess.run(["adb", "devices"],
                                capture_output=True, text=True, check=True)
        print("ADB设备列表:")
        print(result.stdout)

        # 检查是否有设备连接
        lines = result.stdout.strip().split('\n')
        device_lines = [l for l in lines[1:] if l.strip()]  # 跳过标题行

        if not device_lines:
            print("❌ 没有检测到任何ADB设备")
            return False

        # 检查是否有活动设备
        active_devices = [l for l in device_lines if '\tdevice' in l or '\temulator' in l]
        if not active_devices:
            print("❌ 没有活动的ADB设备（设备可能处于未授权状态）")
            return False

        print(f"✅ 找到 {len(active_devices)} 个活动设备")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ 执行adb devices失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到adb命令")
        return False

if __name__ == "__main__":
    print("=== DSMS 连接测试工具 ===\n")

    # 先检查ADB设备
    if not check_adb_devices():
        print("\n请先连接Android设备并确保ADB调试已启用")
        sys.exit(1)

    # 执行连接测试
    test_dsms_connection()