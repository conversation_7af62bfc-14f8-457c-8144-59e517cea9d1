# 🎉 混合虚拟屏幕系统 - 完整使用指南

## ✅ 编译成功！

恭喜！应用已经成功编译。现在让我们来了解如何使用这个强大的虚拟屏幕系统。

## 🚀 快速开始

### 1. 安装和启动应用

```bash
# 安装应用到设备
adb install app/build/outputs/apk/debug/app-debug.apk

# 启动应用
adb shell am start -n com.example.virtualdisplaydemo/.MainActivity
```

### 2. 授予权限

1. **屏幕录制权限**：应用启动后会自动请求MediaProjection权限
2. **点击"允许"**：在系统对话框中授予权限
3. **确认授权**：应用会显示"屏幕捕获权限已授予"

### 3. 创建虚拟屏幕

#### 方法A：手动创建
```bash
1. 点击应用中的"Add Virtual Screen"按钮
2. 系统会创建一个新的虚拟屏幕
3. 虚拟屏幕会显示在应用界面中
```

#### 方法B：通过网络创建（推荐）
```bash
1. 应用会自动启动网络服务器（端口12345）
2. 使用Python客户端连接
3. 自动创建独立的虚拟屏幕
```

## 🐍 Python客户端使用

### 基本连接示例

```python
# 使用提供的Python客户端
python3 python_client_example.py

# 或者手动输入参数
python3 python_client_example.py
# 输入设备IP: *************
# 输入应用包名: com.wsy.crashcatcher
```

### 高级使用示例

```python
#!/usr/bin/env python3
import socket
import json
import time

def connect_and_launch():
    # 连接到Android设备
    client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    client.connect(('*************', 12345))  # 替换为实际IP
    
    # 发送启动应用请求
    request = {
        "action": "launch_app",
        "package_name": "com.wsy.crashcatcher",
        "activity_name": "MainActivity"
    }
    
    client.send(json.dumps(request).encode())
    response = client.recv(1024).decode()
    print(f"响应: {response}")
    
    # 保持连接
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        client.close()

if __name__ == "__main__":
    connect_and_launch()
```

## 📱 应用界面说明

### 主要元素

1. **"Add Virtual Screen"按钮**
   - 手动创建虚拟屏幕
   - 适合单人调试使用

2. **虚拟屏幕列表**
   - 显示所有活跃的虚拟屏幕
   - 实时更新状态信息

3. **状态指示器**
   - 🟢 绿色：应用正在运行
   - 🟡 黄色：网络客户端已连接
   - ⏳ 沙漏：预占用状态
   - ⚪ 白色：手动创建的空闲屏幕
   - ⚫ 黑色：网络创建的空闲屏幕

### 网络状态显示

应用会显示当前的网络连接状态：
```
网络状态: 服务器运行中 | 端口: 12345 | 连接数: 2
```

## 🔧 核心功能特性

### 1. 虚拟显示器管理
- **多屏幕支持**：同时创建多个虚拟屏幕
- **动态分辨率**：根据设备性能自动调整
- **内存优化**：智能管理内存使用

### 2. 网络服务器
- **自动启动**：应用启动时自动开启服务器
- **多客户端支持**：支持多个Python客户端同时连接
- **心跳机制**：自动检测客户端连接状态

### 3. 应用启动管理
- **智能检测**：自动检测应用是否在运行
- **故障恢复**：应用崩溃时自动重启
- **资源清理**：应用退出时自动清理资源

### 4. 优化配置
- **设备适配**：根据设备性能自动调整参数
- **内存监控**：实时监控内存使用情况
- **性能优化**：自动优化系统性能

## 🎯 使用场景

### 场景1：单人开发调试
```bash
1. 启动应用
2. 点击"Add Virtual Screen"
3. 在虚拟屏幕上手动启动目标应用
4. 进行调试工作
```

### 场景2：多人协作调试
```bash
1. 一人启动Android应用（作为服务器）
2. 多人通过Python客户端连接
3. 每个连接自动创建独立的虚拟屏幕
4. 在各自的虚拟屏幕上调试不同应用
```

### 场景3：自动化测试
```python
def automated_testing():
    # 连接到虚拟显示服务
    client = connect_to_server('*************', 12345)
    
    # 启动测试应用
    launch_app(client, "com.test.app")
    
    # 执行测试用例
    run_test_cases(client)
    
    # 收集结果
    collect_results(client)
    
    # 清理资源
    client.close()
```

## 📊 监控和调试

### 查看应用日志
```bash
# 查看所有相关日志
adb logcat | grep "DSMS\|VirtualDisplay\|NetworkServer"

# 查看主要组件日志
adb logcat -s "DSMS_MainActivity"

# 查看网络连接日志
adb logcat -s "NetworkServerManager"
```

### 监控系统状态
```bash
# 查看虚拟显示器状态
adb shell dumpsys display

# 查看应用进程状态
adb shell ps | grep virtualdisplaydemo

# 查看内存使用情况
adb shell dumpsys meminfo com.example.virtualdisplaydemo
```

## 🚨 故障排除

### 常见问题

#### 1. 应用无法启动
```bash
问题：应用安装后无法启动
解决：
1. 检查设备是否支持MediaProjection
2. 确保Android版本 >= 5.0
3. 重新安装应用：adb install -r app-debug.apk
```

#### 2. 权限被拒绝
```bash
问题：屏幕录制权限被拒绝
解决：
1. 重新启动应用
2. 在权限对话框中点击"允许"
3. 检查系统设置中的应用权限
```

#### 3. 网络连接失败
```bash
问题：Python客户端无法连接
解决：
1. 检查设备IP地址：adb shell ip addr show wlan0
2. 确保设备在同一网络
3. 检查防火墙设置
4. 确保应用正在运行
```

#### 4. 虚拟屏幕显示异常
```bash
问题：虚拟屏幕黑屏或显示异常
解决：
1. 重新创建虚拟屏幕
2. 检查目标应用是否支持虚拟显示器
3. 重启应用
```

### 调试技巧

#### 1. 启用详细日志
```bash
# 设置日志级别
adb shell setprop log.tag.DSMS_MainActivity VERBOSE
adb shell setprop log.tag.VirtualDisplayManager VERBOSE
```

#### 2. 重置应用状态
```bash
# 清理应用数据
adb shell pm clear com.example.virtualdisplaydemo

# 重新安装应用
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

#### 3. 网络调试
```bash
# 检查端口是否开放
adb shell netstat -an | grep 12345

# 测试网络连接
telnet <设备IP> 12345
```

## 📈 性能优化建议

### 1. 内存优化
- 避免同时运行过多虚拟屏幕（建议≤5个）
- 定期清理不用的虚拟屏幕
- 监控应用内存使用情况

### 2. 网络优化
- 使用稳定的WiFi连接
- 避免频繁的连接断开
- 实现客户端重连机制

### 3. 系统优化
- 关闭不必要的后台应用
- 确保设备有足够的可用内存（建议≥2GB）
- 定期重启设备清理系统缓存

## 🔄 自动化脚本

### 一键编译和运行
```bash
# 使用提供的脚本
./build_and_run.sh

# 或者手动执行
./gradlew clean assembleDebug
adb install -r app/build/outputs/apk/debug/app-debug.apk
adb shell am start -n com.example.virtualdisplaydemo/.MainActivity
```

### Python客户端批量测试
```bash
# 多会话演示
python3 python_client_example.py demo

# 这会创建多个会话并监控状态
```

## 📝 总结

这个混合虚拟屏幕系统提供了：

1. **✅ 多虚拟屏幕支持**：同时创建多个独立的虚拟屏幕
2. **✅ 网络服务器功能**：支持远程客户端连接
3. **✅ 智能应用管理**：自动启动、监控和恢复应用
4. **✅ 性能优化**：内存管理和系统优化
5. **✅ 故障恢复**：自动检测和恢复机制
6. **✅ 易于使用**：简单的界面和API

通过合理使用，该系统可以支持多人协作调试，同时保持良好的性能和稳定性。

## 🎯 下一步

1. **测试基本功能**：先尝试手动创建虚拟屏幕
2. **网络连接测试**：使用Python客户端连接
3. **多会话测试**：尝试多个客户端同时连接
4. **性能监控**：观察系统资源使用情况
5. **自定义开发**：根据需要扩展功能

如果遇到任何问题，请参考故障排除部分或查看应用日志进行调试。
