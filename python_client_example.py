#!/usr/bin/env python3
"""
混合虚拟屏幕系统 - Python客户端示例

这个脚本演示如何连接到Android应用并创建虚拟屏幕会话。
"""

import socket
import json
import time
import threading
import sys
from typing import Optional

class VirtualDisplayClient:
    def __init__(self, host: str = "*************", port: int = 12345):
        """
        初始化虚拟显示客户端
        
        Args:
            host: Android设备的IP地址
            port: 服务端口（默认12345）
        """
        self.host = host
        self.port = port
        self.socket: Optional[socket.socket] = None
        self.connected = False
        self.session_id = None
        
    def connect(self) -> bool:
        """连接到Android应用"""
        try:
            print(f"🔗 正在连接到 {self.host}:{self.port}...")
            
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10秒超时
            self.socket.connect((self.host, self.port))
            
            self.connected = True
            print("✅ 连接成功!")
            
            # 启动心跳线程
            heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            heartbeat_thread.start()
            
            return True
            
        except socket.timeout:
            print("❌ 连接超时，请检查:")
            print("   1. Android设备IP地址是否正确")
            print("   2. 设备是否在同一网络")
            print("   3. 应用是否正在运行")
            return False
            
        except ConnectionRefusedError:
            print("❌ 连接被拒绝，请检查:")
            print("   1. Android应用是否正在运行")
            print("   2. 端口是否正确")
            print("   3. 防火墙设置")
            return False
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def launch_app(self, package_name: str, activity_name: str = "MainActivity") -> bool:
        """
        在虚拟屏幕上启动应用
        
        Args:
            package_name: 应用包名，如 "com.example.testapp"
            activity_name: Activity名称，如 "MainActivity"
        """
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
        
        try:
            print(f"🚀 正在启动应用: {package_name}")
            
            request = {
                "action": "launch_app",
                "package_name": package_name,
                "activity_name": activity_name,
                "client_id": f"PyClient_{int(time.time())}"
            }
            
            self._send_message(request)
            response = self._receive_message()
            
            if response and response.get("status") == "success":
                self.session_id = response.get("session_id")
                print(f"✅ 应用启动成功! 会话ID: {self.session_id}")
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                print(f"❌ 应用启动失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ 启动应用时出错: {e}")
            return False
    
    def get_session_status(self) -> Optional[dict]:
        """获取会话状态"""
        if not self.connected or not self.session_id:
            return None
        
        try:
            request = {
                "action": "get_status",
                "session_id": self.session_id
            }
            
            self._send_message(request)
            return self._receive_message()
            
        except Exception as e:
            print(f"❌ 获取状态时出错: {e}")
            return None
    
    def stop_app(self) -> bool:
        """停止应用"""
        if not self.connected or not self.session_id:
            return False
        
        try:
            print("🛑 正在停止应用...")
            
            request = {
                "action": "stop_app",
                "session_id": self.session_id
            }
            
            self._send_message(request)
            response = self._receive_message()
            
            if response and response.get("status") == "success":
                print("✅ 应用已停止")
                return True
            else:
                print("❌ 停止应用失败")
                return False
                
        except Exception as e:
            print(f"❌ 停止应用时出错: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        self.connected = False
        
        if self.socket:
            try:
                # 发送断开连接消息
                if self.session_id:
                    request = {
                        "action": "disconnect",
                        "session_id": self.session_id
                    }
                    self._send_message(request)
                
                self.socket.close()
                print("🔌 已断开连接")
                
            except Exception as e:
                print(f"断开连接时出错: {e}")
    
    def _send_message(self, message: dict):
        """发送消息"""
        if self.socket:
            data = json.dumps(message).encode('utf-8')
            self.socket.send(data)
    
    def _receive_message(self) -> Optional[dict]:
        """接收消息"""
        if not self.socket:
            return None
        
        try:
            data = self.socket.recv(1024)
            if data:
                return json.loads(data.decode('utf-8'))
        except Exception as e:
            print(f"接收消息时出错: {e}")
        
        return None
    
    def _heartbeat_loop(self):
        """心跳循环"""
        while self.connected:
            try:
                time.sleep(30)  # 30秒心跳间隔
                
                if self.connected:
                    request = {"action": "heartbeat"}
                    self._send_message(request)
                    
            except Exception as e:
                print(f"心跳发送失败: {e}")
                self.connected = False
                break

def main():
    """主函数 - 演示基本使用"""
    print("🎯 混合虚拟屏幕系统 - Python客户端示例")
    print("=" * 50)
    
    # 获取用户输入
    host = input("请输入Android设备IP地址 (默认: *************): ").strip()
    if not host:
        host = "*************"
    
    package_name = input("请输入要启动的应用包名 (默认: com.wsy.crashcatcher): ").strip()
    if not package_name:
        package_name = "com.wsy.crashcatcher"
    
    # 创建客户端
    client = VirtualDisplayClient(host=host)
    
    try:
        # 连接到服务器
        if not client.connect():
            return
        
        # 启动应用
        if client.launch_app(package_name):
            print("\n📊 会话信息:")
            print(f"   会话ID: {client.session_id}")
            print(f"   应用包名: {package_name}")
            print(f"   服务器: {host}")
            
            # 监控会话状态
            print("\n🔄 开始监控会话状态 (按Ctrl+C退出)...")
            
            while True:
                time.sleep(10)  # 每10秒检查一次状态
                
                status = client.get_session_status()
                if status:
                    print(f"📈 会话状态: {status.get('app_status', '未知')}")
                    
                    # 显示性能信息
                    if 'performance' in status:
                        perf = status['performance']
                        print(f"   内存使用: {perf.get('memory_usage', 0)}MB")
                        print(f"   健康状态: {perf.get('health_score', 0)}")
                else:
                    print("⚠️  无法获取会话状态")
        
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断，正在清理...")
        
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
        
    finally:
        # 清理资源
        client.stop_app()
        client.disconnect()
        print("🧹 清理完成")

def demo_multiple_sessions():
    """演示多会话使用"""
    print("🎯 多会话演示")
    print("=" * 30)
    
    host = "*************"
    apps = [
        "com.wsy.crashcatcher",
        "com.android.settings",
        "com.android.calculator2"
    ]
    
    clients = []
    
    try:
        # 创建多个会话
        for i, app in enumerate(apps):
            print(f"\n🚀 创建会话 {i+1}: {app}")
            
            client = VirtualDisplayClient(host=host)
            if client.connect():
                if client.launch_app(app):
                    clients.append(client)
                    print(f"✅ 会话 {i+1} 创建成功")
                else:
                    client.disconnect()
            
            time.sleep(2)  # 间隔2秒
        
        print(f"\n📊 成功创建 {len(clients)} 个会话")
        
        # 监控所有会话
        print("🔄 监控所有会话状态...")
        for _ in range(10):  # 监控10次
            print(f"\n--- 状态检查 {_+1} ---")
            
            for i, client in enumerate(clients):
                status = client.get_session_status()
                if status:
                    print(f"会话 {i+1}: {status.get('app_status', '未知')}")
                else:
                    print(f"会话 {i+1}: 无响应")
            
            time.sleep(5)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断")
        
    finally:
        # 清理所有会话
        print("\n🧹 清理所有会话...")
        for i, client in enumerate(clients):
            print(f"清理会话 {i+1}...")
            client.stop_app()
            client.disconnect()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_multiple_sessions()
    else:
        main()
